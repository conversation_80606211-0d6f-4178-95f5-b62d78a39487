<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Ultrasound Signal Processing · Module 1</title>
  <link rel="stylesheet" href="../../assets/index-d7x91drb.css">
  <style>
    .container{max-width:960px;margin:0 auto;padding:24px}
    .card{background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:20px;margin:16px 0}
    header,footer{padding:12px 0}
    h1,h2,h3{color:#e6e9ff}
    p,li,dd,dt{color:#b9bfd6}
    .btn{display:inline-block;padding:8px 12px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b}
    .btn:hover{border-color:#3f4580;background:#1c2040}
    .grid-2{display:grid;grid-template-columns:1fr 1fr;gap:16px}
    .note{font-size:.9rem;color:#9fb1ff}
    code{background:#151933;border:1px solid #252a4a;border-radius:6px;padding:2px 6px}
  </style>
</head>
<body>
  <div class="container">
    <header>
      <a class="btn" href="./index.html">← Module 1 Index</a>
      <a class="btn" style="margin-left:8px" href="./ct.html">Previous: CT</a>
      <h1>Ultrasound Signal Processing</h1>
      <p class="note">From transducer physics to beamforming, detection, B‑mode and Doppler.</p>
    </header>

    <section class="card">
      <h2>Learning Objectives</h2>
      <ul>
        <li>Explain piezoelectric transduction and key transducer parameters.</li>
        <li>Describe transmit/receive beamforming and focusing.</li>
        <li>Outline detection chain: demodulation, envelope, log compression.</li>
        <li>Summarize Doppler processing and velocity estimation.</li>
      </ul>
    </section>

    <section class="card">
      <h2>Transducers & Front‑End</h2>
      <div class="grid-2">
        <div>
          <h3>Piezoelectric Elements</h3>
          <ul>
            <li>Center frequency f0 ~ c/(2t) where t is element thickness.</li>
            <li>Bandwidth set by backing and matching layers.</li>
          </ul>
        </div>
        <div>
          <h3>TX/RX Chain</h3>
          <ul>
            <li>Pulse generator → high‑voltage driver → T/R switch.</li>
            <li>LNA → analog filtering → ADC; typical fs ≈ 20–60 MS/s.</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="card">
      <h2>Beamforming</h2>
      <div class="grid-2">
        <div>
          <h3>Delay‑and‑Sum (DAS)</h3>
          <p>Apply element‑dependent delays τ_i and apodization w_i; sample and sum: y(t)=Σ w_i x_i(t+τ_i).</p>
          <ul>
            <li>Dynamic receive focusing improves lateral resolution.</li>
            <li>Apodization reduces sidelobes (Hann, Hamming).</li>
          </ul>
        </div>
        <div>
          <h3>Advanced</h3>
          <ul>
            <li>Plane‑wave compounding (PWI) for ultrafast imaging.</li>
            <li>Adaptive beamforming (MVDR) for contrast improvements.</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="card">
      <h2>Detection & B‑mode</h2>
      <ol>
        <li>IQ demodulation (quadrature mixing at f0 and LPF).</li>
        <li>Envelope via |analytic signal| using Hilbert transform.</li>
        <li>Time‑gain compensation (TGC) for depth attenuation.</li>
        <li>Log compression to fit display dynamic range.</li>
      </ol>
    </section>

    <section class="card">
      <h2>Doppler</h2>
      <ul>
        <li>Doppler shift f_d = 2 v f0 cos(θ) / c.</li>
        <li>Pulsed‑wave (PW) uses packet of pulses; estimate via autocorrelation/FFT.</li>
        <li>Color flow mapping uses mean velocity and variance estimates.</li>
      </ul>
    </section>

    <section class="card">
      <h2>Quick Quiz</h2>
      <ol>
        <li>How does element thickness affect f0?</li>
        <li>Write the DAS beamforming equation.</li>
        <li>What is the purpose of apodization?</li>
        <li>List the main steps to create a B‑mode image from RF data.</li>
        <li>Provide the Doppler velocity equation and define symbols.</li>
      </ol>
      <p class="note">Answers: (1) f0 ∝ 1/t. (2) y(t)=Σ w_i x_i(t+τ_i). (3) Reduce sidelobes. (4) IQ demod→envelope→TGC→log compression→scan conversion. (5) v = f_d c /(2 f0 cosθ).</p>
    </section>

    <footer>
      <a class="btn" href="./contrast.html">Next → Image Contrast Agents</a>
    </footer>
  </div>
</body>
</html>
