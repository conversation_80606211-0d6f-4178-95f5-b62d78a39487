<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>BioMed LMS — Virtual Physiology Lab: 3‑Lead ECG</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
  <style>
    :root{
      --bg:#101828;            /* dark mode base */
      --panel:#0B1220;         /* slightly darker panel bg */
      --panel-2:#111A2C;       /* alt panel bg */
      --muted:#334155;         /* muted text */
      --muted-2:#6B7280;
      --text:#E5E7EB;          /* base text */
      --text-soft:#CBD5E1;
      --accent:#2E90FA;        /* cool blue accent */
      --teal:#22D3EE;          /* vibrant teal for traces */
      --ok:#10B981;            /* success */
      --warn:#F59E0B;
      --danger:#EF4444;
      --card-radius:14px;
      --gap:14px;
      --shadow: 0 10px 30px rgba(0,0,0,.45), inset 0 1px 0 rgba(255,255,255,.02);
      --glass: rgba(255,255,255,0.04);
      --grid-line: rgba(46,144,250,0.15);
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{
      margin:0;
      background: radial-gradient(1200px 800px at 70% -10%, rgba(46,144,250,0.05), transparent 60%),
                  radial-gradient(900px 700px at 20% 110%, rgba(34,211,238,0.05), transparent 60%),
                  var(--bg);
      color:var(--text);
      font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
      overflow:hidden;
    }

    /* Top header (optional breadcrumb/title) */
    .topbar{
      height:56px;
      padding:0 18px;
      display:flex;
      align-items:center;
      justify-content:space-between;
      border-bottom:1px solid rgba(255,255,255,0.06);
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)) ;
      -webkit-backdrop-filter: blur(6px);
      backdrop-filter: blur(6px);
    }
    .brand{
      display:flex; align-items:center; gap:10px;
      font-weight:700; letter-spacing:.2px; color:var(--text);
    }
    .brand-badge{
      width:28px;height:28px;border-radius:8px;
      background: linear-gradient(135deg, var(--accent), var(--teal));
      box-shadow: 0 6px 16px rgba(46,144,250,.35), inset 0 0 18px rgba(34,211,238,.35);
    }
    .crumbs{ color:var(--text-soft); font-size:12px }
    .crumbs strong{ color:var(--text) }
    .actions{ display:flex; gap:8px }
    .top-btn{
      padding:8px 12px; border-radius:10px; border:1px solid rgba(255,255,255,0.08);
      background: var(--glass); color:var(--text);
      cursor:pointer; transition: .2s transform, .2s background;
    }
    .top-btn:hover{ transform: translateY(-1px); background: rgba(46,144,250,0.08) }

    /* Main layout */
    .wrap{
      height: calc(100% - 56px);
      display:grid;
      grid-template-columns: 320px minmax(640px, 1fr) 380px;
      gap: var(--gap);
      padding: var(--gap);
    }

    .panel{
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)),
                  var(--panel);
      border:1px solid rgba(255,255,255,0.06);
      border-radius: var(--card-radius);
      box-shadow: var(--shadow);
      overflow:hidden;
      display:flex; flex-direction:column;
      min-height:0;
    }

    .panel-header{
      padding:14px 16px; border-bottom:1px solid rgba(255,255,255,0.06);
      display:flex; align-items:center; justify-content:space-between;
      background: linear-gradient(180deg, rgba(255,255,255,0.03), rgba(255,255,255,0));
    }
    .panel-title{ font-weight:700; font-size:14px; letter-spacing:.2px }
    .panel-body{ padding:14px; overflow:auto; }

    /* Left: Guided Procedure */
    .checklist{ display:flex; flex-direction:column; gap:10px }
    .step{
      display:grid;
      grid-template-columns: 28px 1fr auto;
      align-items:center;
      gap:10px;
      padding:10px;
      border-radius:12px;
      border:1px solid rgba(255,255,255,0.06);
      background: rgba(255,255,255,0.02);
    }
    .step-icon{
      width:28px; height:28px; border-radius:8px;
      display:grid; place-items:center; color:var(--text);
      background: rgba(255,255,255,0.04); font-size:14px;
    }
    .step-title{ font-weight:600; color:var(--text); font-size:13px }
    .step.done{
      opacity:.6; filter:saturate(.6);
    }
    .step.current{
      border-color: rgba(46,144,250,0.45);
      box-shadow: 0 0 0 3px rgba(46,144,250,0.15);
      background: linear-gradient(180deg, rgba(46,144,250,0.10), rgba(46,144,250,0.03));
    }
    .step .state{
      font-size:12px; color:var(--muted-2);
    }
    .legend{
      margin-top:12px; padding:10px; border-radius:12px;
      background: rgba(34,211,238,0.08);
      border:1px solid rgba(34,211,238,0.3);
      color: var(--text);
      font-size:12px;
    }

    /* Center: Virtual Workbench */
    .workbench{
      background:
        radial-gradient(1000px 500px at 50% 0%, rgba(46,144,250,0.05), transparent 60%),
        linear-gradient(180deg, var(--panel-2), #0a1020 70%);
      position:relative; overflow:hidden;
    }
    .workbench-body{ position:relative; flex:1; min-height:0; }
    .table{
      position:absolute; left:0; right:0; bottom:0; height:34%;
      background: linear-gradient(180deg, #0E1426, #0A0F1F);
      border-top:1px solid rgba(255,255,255,0.08);
      box-shadow: 0 -20px 40px rgba(0,0,0,0.5) inset;
    }

    /* Virtual Patient Torso (photorealistic-styled illustration using gradients) */
    .torso{
      position:absolute; left:50%; top:14%; transform:translateX(-50%);
      width:460px; height:420px; border-radius:220px / 200px;
      background:
        radial-gradient(120px 140px at 50% 25%, rgba(255,255,255,0.05), transparent 60%),
        radial-gradient(200px 240px at 50% 60%, rgba(255,255,255,0.06), transparent 70%),
        linear-gradient(180deg, #1A2236, #0E1527 65%, #0C1222);
      box-shadow: inset 0 -40px 80px rgba(0,0,0,0.35),
                  inset 0 40px 70px rgba(255,255,255,0.05),
                  0 30px 80px rgba(0,0,0,0.55);
      border:1px solid rgba(255,255,255,0.04);
    }
    /* Highlight targets for RA, LA, LL */
    .target{
      position:absolute; width:26px; height:26px; border-radius:50%;
      border:2px solid rgba(34,211,238,0.9);
      box-shadow: 0 0 18px rgba(34,211,238,0.55), inset 0 0 12px rgba(34,211,238,0.3);
      animation:pulse 1.8s ease-in-out infinite;
    }
    @keyframes pulse {
      0%,100%{ transform:scale(1); opacity:1 }
      50%{ transform:scale(1.12); opacity:.85 }
    }
    /* approximate anatomical placement */
    .tgt-ra{ left:calc(50% + 140px); top: 42% }
    .tgt-la{ left:calc(50% - 170px); top: 44% }
    .tgt-ll{ left:calc(50% - 30px); top: 78% }

    /* Electrodes & wires */
    .electrode{
      position:absolute; width:22px; height:22px; border-radius:50%;
      background: radial-gradient(circle at 35% 35%, #E5E7EB, #9CA3AF 60%, #6B7280);
      border:2px solid #374151;
      box-shadow: 0 4px 8px rgba(0,0,0,.4), inset 0 1px 0 rgba(255,255,255,.6);
    }
    .electrode.ra{ left:calc(50% + 140px); top: 42% } /* already placed */
    .electrode.dragging{
      left:calc(50% - 110px); top: 41%;
      box-shadow: 0 8px 20px rgba(0,0,0,.6), inset 0 1px 0 rgba(255,255,255,.6);
      outline:2px dashed rgba(46,144,250,.7);
      outline-offset:3px;
    }
    .wire{
      position:absolute; pointer-events:none;
      height:2px; background: linear-gradient(90deg, rgba(46,144,250,.0), rgba(46,144,250,.9));
      filter: drop-shadow(0 0 4px rgba(46,144,250,.6));
    }
    /* simple bezier-ish wire using rotated div segments */
    .wire.la{ left: 38%; top: 44.5%; width: 22%; transform-origin:left center; transform: rotate(4deg) }
    .wire.la::after{
      content:""; position:absolute; right:-2px; top:-3px; width:8px; height:8px; border-radius:50%; background: var(--accent);
      box-shadow:0 0 8px rgba(46,144,250,.7);
    }

    /* DAQ unit on table */
    .daq{
      position:absolute; right:8%; bottom:8%; width:360px; height:160px;
      background:
        radial-gradient(240px 140px at 70% 0%, rgba(255,255,255,0.05), transparent 60%),
        linear-gradient(180deg, #0E1528, #0A1021);
      border:1px solid rgba(255,255,255,0.08);
      border-radius:16px; box-shadow: 0 14px 30px rgba(0,0,0,0.45), inset 0 0 0 1px rgba(255,255,255,0.04);
      overflow:hidden;
    }
    .daq-header{
      padding:10px 12px; font-size:12px; color: var(--text-soft);
      display:flex; align-items:center; justify-content:space-between;
      border-bottom:1px solid rgba(255,255,255,0.06);
    }
    .power{
      width:14px;height:14px;border-radius:50%;
      box-shadow: 0 0 10px rgba(16,185,129,.7), inset 0 0 10px rgba(16,185,129,.45);
      background: radial-gradient(circle at 30% 30%, #6EE7B7, #10B981 70%, #065F46);
    }
    .daq-body{ padding:10px 12px; position:relative; height: calc(100% - 40px) }
    .port-row{ display:flex; gap:10px; margin-top:6px }
    .port{
      flex:1; height:52px; border-radius:10px;
      display:flex; align-items:center; justify-content:center; gap:6px;
      background: rgba(255,255,255,0.03);
      border:1px solid rgba(255,255,255,0.06);
      position:relative;
    }
    .port-label{
      font-size:11px; letter-spacing:.6px; color:var(--text-soft);
      border:1px solid rgba(255,255,255,0.06);
      padding:2px 6px; border-radius:6px;
      background: rgba(255,255,255,0.02);
    }
    .port.ready{
      border-color: rgba(34,211,238,0.5);
      box-shadow: 0 0 0 3px rgba(34,211,238,0.15), inset 0 0 12px rgba(34,211,238,0.12);
    }
    .port-dot{
      width:8px;height:8px;border-radius:50%; background: var(--teal);
      box-shadow: 0 0 10px rgba(34,211,238,.7);
    }

    /* Right: Instrumentation & Analysis */
    .module{ margin-bottom:var(--gap); background:rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06); border-radius:12px; overflow:hidden }
    .module-header{ padding:10px 12px; border-bottom:1px solid rgba(255,255,255,0.06); display:flex; align-items:center; justify-content:space-between; }
    .module-title{ font-weight:700; font-size:13px }
    .module-body{ padding:12px }

    /* Oscilloscope */
    .scope{
      background: radial-gradient(800px 400px at 80% 0%, rgba(46,144,250,0.06), transparent 60%),
                  #0A1020;
      border:1px solid rgba(46,144,250,.25);
      border-radius:10px; overflow:hidden; position:relative; height:200px;
      box-shadow: inset 0 0 40px rgba(46,144,250,0.08);
    }
    .grid{
      position:absolute; inset:0; background-image:
        linear-gradient(to right, var(--grid-line) 1px, transparent 1px),
        linear-gradient(to bottom, var(--grid-line) 1px, transparent 1px);
      background-size: 24px 24px, 24px 24px;
      opacity:.9; pointer-events:none;
    }
    canvas#ecg{ position:absolute; left:0; top:0; width:100%; height:100% }

    .scope-controls{ margin-top:8px; display:grid; grid-template-columns: 1fr 1fr; gap:10px }
    .knob{
      background: rgba(255,255,255,0.02);
      border:1px solid rgba(255,255,255,0.06);
      border-radius:10px; padding:8px;
    }
    .knob label{ display:block; font-size:12px; margin-bottom:6px; color:var(--text-soft) }
    .knob .row{ display:flex; align-items:center; gap:10px }
    .knob input[type=range]{ width:100% }
    .knob .val{ font-size:12px; color:var(--text) }

    /* Measurements & Calibration */
    .readouts{
      display:grid; grid-template-columns: repeat(3, 1fr); gap:10px;
    }
    .readout{
      background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06);
      border-radius:10px; padding:10px;
    }
    .label{ font-size:11px; color:var(--muted-2) }
    .value{ font-size:18px; font-weight:800; letter-spacing:.3px; margin-top:4px;
      background: linear-gradient(180deg, #E5F2FF, #96C9FF 60%, #5AAFFF);
      -webkit-background-clip: text; background-clip: text; color: transparent;
      text-shadow: 0 0 18px rgba(46,144,250,.25);
    }
    .cal-block{
      margin-top:12px; display:grid; grid-template-columns: 1fr auto; gap:10px; align-items:center;
    }
    .cal-scope{
      height:60px; background:#0A1020; border:1px solid rgba(46,144,250,.25); border-radius:8px; position:relative; overflow:hidden;
    }
    .cal-scope .grid{ background-size: 16px 16px, 16px 16px; opacity:.8 }
    .cal-btn{
      padding:10px 14px; border-radius:10px; font-weight:700; letter-spacing:.3px;
      background: linear-gradient(180deg, #2E90FA, #1C6CD4);
      border:1px solid rgba(255,255,255,0.1);
      color:white; cursor:pointer; box-shadow: 0 10px 20px rgba(46,144,250,.25), inset 0 -2px 0 rgba(0,0,0,.25);
      transition:.2s transform, .2s filter;
    }
    .cal-btn:hover{ transform: translateY(-1px); filter: brightness(1.05) }
    .status{
      margin-top:8px; font-size:12px; color:var(--text-soft);
    }
    .status.ok{ color:var(--ok) }

    /* Component Toolbox */
    .toolbox{ display:grid; grid-template-columns: repeat(4, 1fr); gap:10px }
    .tool{
      background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06);
      border-radius:10px; padding:10px; height:86px; display:flex; flex-direction:column; justify-content:space-between;
      cursor:grab; -webkit-user-select:none; user-select:none; transition:.2s transform, .2s box-shadow;
    }
    .tool:hover{ transform: translateY(-2px); box-shadow: 0 10px 24px rgba(0,0,0,.35) }
    .tool .icon{
      width:28px; height:28px; border-radius:8px; display:grid; place-items:center;
      background: linear-gradient(135deg, rgba(46,144,250,0.2), rgba(34,211,238,0.18));
      border:1px solid rgba(46,144,250,0.35); color:#BFE4FF; font-weight:900;
      text-shadow: 0 0 8px rgba(46,144,250,.4);
    }
    .tool .name{ font-size:12px; color:var(--text); font-weight:600 }

    /* Bottom Bar */
    .bottombar{
      position:fixed; left:var(--gap); right:var(--gap); bottom:var(--gap);
      height:64px; padding:10px 12px; border-radius:14px;
      background: linear-gradient(180deg, rgba(255,255,255,0.04), rgba(255,255,255,0.02));
      border:1px solid rgba(255,255,255,0.08);
      display:flex; align-items:center; justify-content:space-between; gap:10px;
      box-shadow: var(--shadow);
    }
    .fx-btn{
      display:flex; align-items:center; gap:8px;
      padding:10px 12px; border-radius:10px; border:1px solid rgba(255,255,255,0.08);
      background: rgba(46,144,250,0.08); color:var(--text); cursor:pointer;
    }
    .fx-badge{ width:28px; height:28px; border-radius:8px; display:grid; place-items:center;
      background: linear-gradient(135deg, rgba(46,144,250,0.3), rgba(34,211,238,0.2)); border:1px solid rgba(46,144,250,0.4);
      color:#D3F3FF; font-weight:900 }
    .formula-pop{
      position:absolute; left:12px; bottom:68px; width:340px;
      background: var(--panel); border:1px solid rgba(255,255,255,0.08); border-radius:12px; padding:12px;
      box-shadow: var(--shadow); display:none;
    }
    .formula-pop.show{ display:block }
    .report-btn{
      margin-left:auto;
      padding:12px 16px; border-radius:12px; font-weight:800; letter-spacing:.3px;
      background: linear-gradient(180deg, #2E90FA, #1C6CD4);
      border:1px solid rgba(255,255,255,0.12); color:white; cursor:pointer;
      box-shadow: 0 16px 28px rgba(46,144,250,.28), inset 0 -2px 0 rgba(0,0,0,.3);
      display:flex; align-items:center; gap:8px;
    }
    .report-btn:hover{ filter: brightness(1.05) }

    /* Responsive */
    @media (max-width: 1200px){
      .wrap{ grid-template-columns: 300px 1fr 360px }
      .torso{ width:420px; height:380px }
    }
    @media (max-width: 1024px){
      body{ overflow:auto }
      .wrap{ height:auto; grid-template-columns: 1fr; grid-auto-rows:minmax(260px, auto) }
      .bottombar{ position:static; margin-top:var(--gap) }
    }
  </style>
</head>
<body>
  <div class="topbar">
    <div class="brand">
      <div class="brand-badge"></div>
      BioMed LMS
      <span class="crumbs">/ Virtual Physiology Lab / <strong>3‑Lead ECG</strong></span>
    </div>
    <div class="actions">
      <button class="top-btn" onclick="history.back()">Back</button>
      <a class="top-btn" href="../index.html">Home</a>
    </div>
  </div>

  <div class="wrap">
    <!-- Left Panel: Guided Procedure -->
    <section class="panel" aria-label="Guided Procedure">
      <div class="panel-header">
        <div class="panel-title">Experiment Setup: 3‑Lead ECG</div>
      </div>
      <div class="panel-body">
        <div class="checklist">
          <div class="step done">
            <div class="step-icon">⏻</div>
            <div class="step-title">1. Power On Data Acquisition Unit</div>
            <div class="state">✔ Done</div>
          </div>
          <div class="step current">
            <div class="step-icon">◎</div>
            <div class="step-title">2. Place Electrodes on Virtual Patient</div>
            <div class="state" style="color:var(--accent)">In Progress</div>
          </div>
          <div class="step">
            <div class="step-icon">🔌</div>
            <div class="step-title">3. Connect Leads to DAQ Module</div>
            <div class="state">Pending</div>
          </div>
          <div class="step">
            <div class="step-icon">∿</div>
            <div class="step-title">4. Apply Calibration Signal</div>
            <div class="state">Pending</div>
          </div>
          <div class="step">
            <div class="step-icon">⏺</div>
            <div class="step-title">5. Record Resting‑State ECG</div>
            <div class="state">Pending</div>
          </div>
        </div>

        <div class="legend">
          Place RA (right arm), LA (left arm), and LL (left leg) electrodes on the highlighted targets. Use conductive gel for optimal contact.
        </div>
      </div>
    </section>

    <!-- Center Panel: Virtual Workbench -->
    <section class="panel workbench" aria-label="Virtual Workbench">
      <div class="panel-header">
        <div class="panel-title">Virtual Workbench</div>
        <div style="font-size:12px;color:var(--text-soft)">Mid‑procedure: Electrode placement and lead routing</div>
      </div>
      <div class="workbench-body">
        <!-- Torso -->
        <div class="torso" aria-label="Virtual Patient Torso"></div>

        <!-- Electrode targets -->
        <div class="target tgt-ra" title="RA target"></div>
        <div class="target tgt-la" title="LA target"></div>
        <div class="target tgt-ll" title="LL target"></div>

        <!-- One electrode already placed (RA) -->
        <div class="electrode ra" title="RA electrode placed"></div>

        <!-- Second electrode being dragged toward LA -->
        <div class="electrode dragging" title="Dragging LA electrode"></div>
        <div class="wire la"></div>

        <!-- DAQ unit -->
        <div class="daq" aria-label="DAQ Unit">
          <div class="daq-header">
            <div>BioMed Mini‑DAQ v2</div>
            <div class="power" title="Power On"></div>
          </div>
          <div class="daq-body">
            <div style="font-size:11px;color:var(--text-soft);">Lead Ports</div>
            <div class="port-row">
              <div class="port ready" title="Right Arm">
                <span class="port-dot"></span>
                <span class="port-label">RA</span>
              </div>
              <div class="port ready" title="Left Arm">
                <span class="port-dot"></span>
                <span class="port-label">LA</span>
              </div>
              <div class="port ready" title="Left Leg">
                <span class="port-dot"></span>
                <span class="port-label">LL</span>
              </div>
            </div>
            <div style="margin-top:10px; display:flex; gap:10px; align-items:center;">
              <div style="font-size:11px;color:var(--text-soft)">Status:</div>
              <div style="font-size:12px;color:var(--ok); font-weight:700">Ready • Streaming Enabled</div>
            </div>
          </div>
        </div>

        <!-- Table surface -->
        <div class="table"></div>
      </div>
    </section>

    <!-- Right Panel: Instrumentation & Analysis -->
    <aside class="panel" aria-label="Instrumentation and Data Analysis">
      <div class="panel-header">
        <div class="panel-title">Instrumentation & Analysis</div>
      </div>
      <div class="panel-body" style="display:flex; flex-direction:column; gap:14px;">
        <!-- A. Oscilloscope -->
        <div class="module">
          <div class="module-header">
            <div class="module-title">Live ECG Waveform</div>
            <div style="font-size:11px;color:var(--text-soft)">Lead II</div>
          </div>
          <div class="module-body">
            <div class="scope">
              <div class="grid"></div>
              <canvas id="ecg"></canvas>
            </div>
            <div class="scope-controls">
              <div class="knob">
                <label for="timeDiv">Time/Div (ms)</label>
                <div class="row">
                  <input id="timeDiv" type="range" min="50" max="500" step="10" value="200" />
                  <div class="val" id="timeDivVal">200</div>
                </div>
              </div>
              <div class="knob">
                <label for="ampDiv">Amplitude (mV)</label>
                <div class="row">
                  <input id="ampDiv" type="range" min="0.5" max="2.5" step="0.1" value="1.0" />
                  <div class="val" id="ampDivVal">1.0</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- B. Measurements & Calibration -->
        <div class="module">
          <div class="module-header">
            <div class="module-title">Output Readings & Settings</div>
            <div style="font-size:11px;color:var(--text-soft)">Sampling: 500 Hz</div>
          </div>
          <div class="module-body">
            <div class="readouts">
              <div class="readout">
                <div class="label">Heart Rate</div>
                <div class="value" id="hrVal">72 BPM</div>
              </div>
              <div class="readout">
                <div class="label">R‑R Interval</div>
                <div class="value" id="rrVal">833 ms</div>
              </div>
              <div class="readout">
                <div class="label">P‑R Interval</div>
                <div class="value">150 ms</div>
              </div>
            </div>

            <div class="cal-block">
              <div class="cal-scope">
                <div class="grid"></div>
                <canvas id="calSquare"></canvas>
              </div>
              <button class="cal-btn" id="calibrateBtn">Calibrate</button>
            </div>
            <div class="status" id="calStatus">Status: <span class="ok">Calibrated ✔</span></div>
          </div>
        </div>

        <!-- C. Component Toolbox -->
        <div class="module">
          <div class="module-header">
            <div class="module-title">Sensors & Tools</div>
            <div style="font-size:11px;color:var(--text-soft)">Drag to Workbench</div>
          </div>
          <div class="module-body">
            <div class="toolbox">
              <div class="tool" title="ECG Electrode">
                <div class="icon">E</div>
                <div class="name">ECG Electrode</div>
                <div style="font-size:11px;color:var(--muted-2)">x3</div>
              </div>
              <div class="tool" title="Conductive Gel">
                <div class="icon">G</div>
                <div class="name">Conductive Gel</div>
              </div>
              <div class="tool" title="Lead Wire (Red)">
                <div class="icon" style="background:linear-gradient(135deg, rgba(239,68,68,.3), rgba(239,68,68,.18)); border-color:rgba(239,68,68,.45)">R</div>
                <div class="name">Lead Wire</div>
                <div style="font-size:11px;color:#FCA5A5">Red</div>
              </div>
              <div class="tool" title="Lead Wire (Green)">
                <div class="icon" style="background:linear-gradient(135deg, rgba(16,185,129,.3), rgba(16,185,129,.18)); border-color:rgba(16,185,129,.45)">G</div>
                <div class="name">Lead Wire</div>
                <div style="font-size:11px;color:#86EFAC">Green</div>
              </div>
              <div class="tool" title="Lead Wire (Black)">
                <div class="icon" style="background:linear-gradient(135deg, rgba(156,163,175,.35), rgba(156,163,175,.2)); border-color:rgba(156,163,175,.45)">B</div>
                <div class="name">Lead Wire</div>
                <div style="font-size:11px;color:#E5E7EB">Black</div>
              </div>
              <div class="tool" title="Ruler">
                <div class="icon">R</div>
                <div class="name">Ruler</div>
              </div>
              <div class="tool" title="Spare Electrode">
                <div class="icon">E</div>
                <div class="name">Electrode</div>
              </div>
              <div class="tool" title="Spare Gel">
                <div class="icon">G</div>
                <div class="name">Gel</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  </div>

  <!-- Bottom Bar -->
  <div class="bottombar" role="toolbar" aria-label="Knowledge & Reporting">
    <div class="fx-btn" id="fxBtn" title="Formulas & Equations">
      <div class="fx-badge">ƒx</div>
      <div style="font-weight:700">Formulas & Equations</div>
    </div>
    <div class="formula-pop" id="formulaPop">
      <div style="font-weight:800; margin-bottom:6px">Key Formula</div>
      <div style="font-size:13px; color:var(--text-soft)">
        Heart Rate (BPM) = 60 / R‑R Interval (s)
      </div>
      <div style="margin-top:10px; font-size:12px; color:var(--muted-2)">
        Example: R‑R = 0.833 s ⇒ HR ≈ 72 BPM
      </div>
    </div>
    <button class="report-btn" id="reportBtn">
      <span style="font-size:14px">📄</span>
      Generate Lab Report
    </button>
  </div>

  <script>
    // Simple helpers
    const $ = (sel) => document.querySelector(sel);

    // Scope canvas sizing
    function fitCanvas(canvas){
      const dpr = window.devicePixelRatio || 1;
      const rect = canvas.getBoundingClientRect();
      canvas.width = Math.round(rect.width * dpr);
      canvas.height = Math.round(rect.height * dpr);
      const ctx = canvas.getContext('2d');
      ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
      return ctx;
    }

    // ECG waveform generator (stylized P-QRS-T sequence)
    const ecgCanvas = document.getElementById('ecg');
    let ecgCtx = fitCanvas(ecgCanvas);
    let tDiv = 200; // ms/div initial
    let ampDiv = 1.0; // mV scale initial
    const timeDivEl = document.getElementById('timeDiv');
    const timeDivVal = document.getElementById('timeDivVal');
    const ampDivEl = document.getElementById('ampDiv');
    const ampDivVal = document.getElementById('ampDivVal');

    timeDivEl.addEventListener('input', (e)=> {
      tDiv = Number(e.target.value);
      timeDivVal.textContent = tDiv;
    });
    ampDivEl.addEventListener('input', (e)=> {
      ampDiv = Number(e.target.value);
      ampDivVal.textContent = ampDiv.toFixed(1);
    });

    let x = 0;
    function ecgSample(phase){
      // Build one heartbeat: total ~0.8s at 72 BPM
      // Phase [0, 1)
      let y = 0;
      // P-wave (~0.1s wide, small amplitude)
      if (phase >= 0.05 && phase < 0.15) {
        const p = (phase - 0.1) / 0.05;
        y += 0.15 * Math.exp(-p*p*8);
      }
      // Q dip
      if (phase >= 0.20 && phase < 0.23){
        const q = (phase - 0.215) / 0.015;
        y -= 0.35 * Math.exp(-q*q*10);
      }
      // R spike
      if (phase >= 0.23 && phase < 0.265){
        const r = (phase - 0.2475) / 0.0175;
        y += 1.0 * Math.exp(-r*r*22);
      }
      // S dip
      if (phase >= 0.265 && phase < 0.31){
        const s = (phase - 0.2875) / 0.0225;
        y -= 0.45 * Math.exp(-s*s*16);
      }
      // T-wave (~0.16s wide)
      if (phase >= 0.45 && phase < 0.65){
        const tt = (phase - 0.55) / 0.10;
        y += 0.25 * Math.exp(-tt*tt*6);
      }
      // small baseline wander
      y += 0.02 * Math.sin(phase * Math.PI * 2 * 0.6);
      return y;
    }

    let lastTs = 0;
    let rrIntervalMs = 833; // 72 BPM reference
    function drawECG(ts){
      if (!lastTs) lastTs = ts;
      const dt = Math.min(32, ts - lastTs); // cap
      lastTs = ts;

      const rect = ecgCanvas.getBoundingClientRect();
      const w = rect.width, h = rect.height;
      // scroll effect: shift left
      const imageData = ecgCtx.getImageData(1, 0, Math.max(1, ecgCanvas.width-1), ecgCanvas.height);
      ecgCtx.putImageData(imageData, 0, 0);
      // clear last column
      ecgCtx.clearRect(w-1, 0, 1, h);

      // compute phase increment based on chosen time/div
      // assume 10 divisions width
      const msPerScreen = tDiv * 10;
      const phasePerMs = 1 / rrIntervalMs;
      const pxPerMs = w / msPerScreen;

      x += dt;
      const phase = ((x % rrIntervalMs) / rrIntervalMs);
      const yVal = ecgSample(phase) * ampDiv; // mV scaled
      const yMid = h * 0.55;
      const yPx = yMid - yVal * (h * 0.18);

      // glow trace
      const gx = w - 2;
      ecgCtx.lineWidth = 2;
      ecgCtx.strokeStyle = 'rgba(34,211,238,0.9)';
      ecgCtx.beginPath();
      ecgCtx.moveTo(gx, yPx);
      ecgCtx.lineTo(gx+1, yPx);
      ecgCtx.stroke();

      // head glow
      ecgCtx.fillStyle = 'rgba(34,211,238,0.6)';
      ecgCtx.beginPath();
      ecgCtx.arc(gx+1, yPx, 2.6, 0, Math.PI*2);
      ecgCtx.fill();

      // occasional update of HR / RR
      if (Math.random() < 0.02){
        document.getElementById('hrVal').textContent = '72 BPM';
        document.getElementById('rrVal').textContent = '833 ms';
      }

      requestAnimationFrame(drawECG);
    }

    // Calibration square wave
    const calCanvas = document.getElementById('calSquare');
    let calCtx = fitCanvas(calCanvas);
    let calPhase = 0;
    function drawCal(){
      const rect = calCanvas.getBoundingClientRect();
      const w = rect.width, h = rect.height;

      // Shift
      const img = calCtx.getImageData(1, 0, Math.max(1, calCanvas.width-1), calCanvas.height);
      calCtx.putImageData(img, 0, 0);
      calCtx.clearRect(w-1, 0, 1, h);

      // 1 mV square wave visualization
      const amp = h*0.30;
      const base = h*0.6;
      const high = base - amp;
      const low = base + amp*0.05;

      calPhase = (calPhase + 1) % 40;
      const isHigh = calPhase < 20;
      const y = isHigh ? high : low;

      const gx = w - 2;
      calCtx.fillStyle = 'rgba(46,144,250,0.95)';
      calCtx.fillRect(gx, y, 2, 2);

      requestAnimationFrame(drawCal);
    }

    // Interactions: formula popup and "report"
    const fxBtn = document.getElementById('fxBtn');
    const formulaPop = document.getElementById('formulaPop');
    fxBtn.addEventListener('mouseenter', ()=> formulaPop.classList.add('show'));
    fxBtn.addEventListener('mouseleave', ()=> formulaPop.classList.remove('show'));
    fxBtn.addEventListener('click', ()=> formulaPop.classList.toggle('show'));

    document.getElementById('reportBtn').addEventListener('click', ()=>{
      // Placeholder action: could trigger export/print in future iteration
      const msg = 'Lab Report will include: setup steps, electrode positions, DAQ status, calibration status, and ECG waveform snapshot.';
      alert(msg);
    });

    // Calibrate button interaction
    const calBtn = document.getElementById('calibrateBtn');
    const calStatus = document.getElementById('calStatus');
    calBtn.addEventListener('click', ()=>{
      calBtn.disabled = true;
      calBtn.textContent = 'Calibrating...';
      calStatus.innerHTML = 'Status: Running calibration ∿';

      setTimeout(()=>{
        calBtn.disabled = false;
        calBtn.textContent = 'Calibrate';
        calStatus.innerHTML = 'Status: <span class="ok">Calibrated ✔</span>';
      }, 1200);
    });

    // Handle resize
    window.addEventListener('resize', ()=>{
      ecgCtx = fitCanvas(ecgCanvas);
      calCtx = fitCanvas(calCanvas);
    });

    // Start animations
    requestAnimationFrame(drawECG);
    requestAnimationFrame(drawCal);
  </script>
</body>
</html>
