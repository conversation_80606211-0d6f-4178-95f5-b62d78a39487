<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PET Laboratory - Biomedical Imaging</title>
  <style>
    :root {
      --bg: #101828;
      --panel: #1a2332;
      --text: #f8fafc;
      --soft: #cbd5e1;
      --muted: #64748b;
      --accent: #2E90FA;
      --teal: #22d3ee;
      --ok: #10b981;
      --warn: #f59e0b;
      --error: #ef4444;
      --purple: #8b5cf6;
      --pink: #ec4899;
      --radius: 12px;
      --shadow: 0 20px 40px rgba(0,0,0,0.3);
      --glow: 0 0 20px rgba(139,92,246,0.3);
      --gap: 16px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: var(--bg);
      color: var(--text);
      line-height: 1.6;
      overflow-x: hidden;
    }

    /* Header */
    .header {
      background: linear-gradient(135deg, var(--panel), rgba(139,92,246,0.1));
      border-bottom: 1px solid rgba(255,255,255,0.1);
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .pet-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--purple), var(--pink));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 0 20px rgba(139,92,246,0.3);
      animation: glow 3s ease-in-out infinite;
    }

    @keyframes glow {
      0%, 100% { box-shadow: 0 0 20px rgba(139,92,246,0.3); }
      50% { box-shadow: 0 0 30px rgba(139,92,246,0.6); }
    }

    .title-text h1 {
      font-size: 24px;
      font-weight: 800;
      margin-bottom: 4px;
    }

    .title-text p {
      font-size: 14px;
      color: var(--muted);
    }

    .nav-actions {
      display: flex;
      gap: 12px;
    }

    .nav-btn {
      background: rgba(255,255,255,0.05);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 8px;
      color: var(--soft);
      padding: 10px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .nav-btn:hover {
      background: rgba(139,92,246,0.1);
      border-color: var(--purple);
      color: var(--purple);
    }

    .nav-btn.primary {
      background: linear-gradient(135deg, var(--purple), var(--pink));
      border-color: var(--purple);
      color: white;
    }

    /* Main Container */
    .container {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      gap: 24px;
      padding: 24px;
      height: calc(100vh - 88px);
    }

    /* Panel Styles */
    .panel {
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
      border: 1px solid rgba(255,255,255,0.08);
      border-radius: 16px;
      box-shadow: var(--shadow);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .panel-header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid rgba(255,255,255,0.08);
      background: linear-gradient(180deg, rgba(139,92,246,0.08), transparent);
    }

    .panel-title {
      font-weight: 800;
      font-size: 16px;
      color: var(--text);
      margin-bottom: 4px;
    }

    .panel-subtitle {
      font-size: 12px;
      color: var(--muted);
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
    }

    /* PET Scanner Visualization */
    .pet-scanner {
      width: 100%;
      height: 400px;
      background: radial-gradient(ellipse at center, #1e293b 0%, #0f172a 100%);
      border-radius: 12px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(139,92,246,0.3);
    }

    .scanner-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 280px;
      height: 280px;
      border-radius: 50%;
      background: linear-gradient(45deg, #374151, #4b5563);
      border: 4px solid var(--purple);
      box-shadow: inset 0 0 30px rgba(139,92,246,0.3);
    }

    .detector-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 240px;
      height: 240px;
      border-radius: 50%;
      border: 3px solid rgba(139,92,246,0.6);
      background: conic-gradient(from 0deg, 
        rgba(139,92,246,0.3) 0deg, 
        rgba(236,72,153,0.3) 90deg, 
        rgba(139,92,246,0.3) 180deg, 
        rgba(236,72,153,0.3) 270deg);
      animation: rotate 8s linear infinite;
    }

    .scanner-bore {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 180px;
      height: 180px;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(139,92,246,0.1), transparent);
      border: 2px solid rgba(139,92,246,0.5);
    }

    .patient-table {
      position: absolute;
      bottom: 15%;
      left: 50%;
      transform: translateX(-50%);
      width: 300px;
      height: 60px;
      background: linear-gradient(135deg, #374151, #4b5563);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    }

    .positron-emission {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 150px;
      height: 150px;
      background: radial-gradient(circle, 
        rgba(139,92,246,0.4) 0%, 
        rgba(236,72,153,0.3) 30%, 
        transparent 70%);
      border-radius: 50%;
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes rotate {
      from { transform: translate(-50%, -50%) rotate(0deg); }
      to { transform: translate(-50%, -50%) rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
      50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
    }

    /* Control Panels */
    .control-group {
      margin-bottom: 24px;
      background: rgba(255,255,255,0.02);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: 12px;
      padding: 20px;
    }

    .control-title {
      font-size: 14px;
      font-weight: 700;
      color: var(--purple);
      margin-bottom: 16px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .parameter-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .parameter-label {
      font-size: 12px;
      color: var(--soft);
      font-weight: 600;
    }

    .parameter-value {
      font-size: 14px;
      font-weight: 700;
      color: var(--purple);
      background: rgba(139,92,246,0.1);
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid rgba(139,92,246,0.2);
    }

    .slider-container {
      margin-top: 8px;
    }

    .slider {
      width: 100%;
      height: 6px;
      border-radius: 3px;
      background: rgba(255,255,255,0.1);
      appearance: none;
      outline: none;
      cursor: pointer;
    }

    .slider::-webkit-slider-thumb {
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--purple);
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(139,92,246,0.4);
    }

    /* Tracer Selector */
    .tracer-selector {
      display: grid;
      grid-template-columns: 1fr;
      gap: 8px;
      margin-bottom: 20px;
    }

    .tracer-btn {
      background: rgba(139,92,246,0.1);
      border: 1px solid rgba(139,92,246,0.2);
      border-radius: 8px;
      color: var(--purple);
      padding: 12px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      font-weight: 600;
    }

    .tracer-btn:hover {
      background: rgba(139,92,246,0.2);
      border-color: var(--purple);
    }

    .tracer-btn.active {
      background: var(--purple);
      color: white;
    }

    /* Scan Button */
    .scan-button {
      width: 100%;
      padding: 16px;
      background: linear-gradient(135deg, var(--purple), var(--pink));
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(139,92,246,0.3);
      margin-top: 24px;
    }

    .scan-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(139,92,246,0.4);
    }

    .scan-button.scanning {
      background: linear-gradient(135deg, var(--error), #dc2626);
      animation: scanPulse 2s ease-in-out infinite;
    }

    @keyframes scanPulse {
      0%, 100% { box-shadow: 0 4px 16px rgba(239,68,68,0.3); }
      50% { box-shadow: 0 8px 32px rgba(239,68,68,0.6); }
    }

    /* Image Display */
    .image-display {
      background: #000;
      border-radius: 12px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid rgba(139,92,246,0.2);
    }

    .pet-image {
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, #1a202c 20%, #000 80%);
      border-radius: 8px;
      position: relative;
      opacity: 0.9;
    }

    .metabolic-activity {
      position: absolute;
      top: 30%;
      left: 40%;
      width: 60px;
      height: 60px;
      background: radial-gradient(circle, 
        rgba(255,215,0,0.8) 0%, 
        rgba(255,165,0,0.6) 50%, 
        rgba(255,69,0,0.4) 100%);
      border-radius: 50%;
      animation: metabolicPulse 3s ease-in-out infinite;
    }

    .brain-outline {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 180px;
      border: 2px solid rgba(139,92,246,0.4);
      border-radius: 50% 50% 45% 45%;
      background: linear-gradient(45deg, rgba(139,92,246,0.1), transparent);
    }

    @keyframes metabolicPulse {
      0%, 100% { opacity: 0.6; transform: scale(1); }
      50% { opacity: 1; transform: scale(1.2); }
    }

    .image-overlay {
      position: absolute;
      top: 16px;
      left: 16px;
      background: rgba(0,0,0,0.8);
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      color: var(--soft);
    }

    .activity-scale {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      gap: 10px;
      font-size: 10px;
      color: var(--muted);
    }

    .scale-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .scale-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="header-title">
      <div class="pet-icon">☢️</div>
      <div class="title-text">
        <h1>PET Scanner Laboratory</h1>
        <p>Positron Emission Tomography - Molecular Imaging</p>
      </div>
    </div>
    <div class="nav-actions">
      <a href="biomedical-imaging-instrumentation-splash.html" class="nav-btn">
        ← Back to Main Lab
      </a>
      <button class="nav-btn primary" onclick="startWholeBodScan()">
        Whole Body Scan
      </button>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container">
    <!-- Left Panel: PET Parameters & Tracers -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">PET Parameters & Tracers</div>
        <div class="panel-subtitle">Radiopharmaceutical and acquisition settings</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Radiopharmaceutical</div>
          <div class="tracer-selector">
            <button class="tracer-btn active" onclick="selectTracer('fdg')">
              ¹⁸F-FDG (Glucose Metabolism)
            </button>
            <button class="tracer-btn" onclick="selectTracer('florbetapir')">
              ¹⁸F-Florbetapir (Amyloid)
            </button>
            <button class="tracer-btn" onclick="selectTracer('flutemetamol')">
              ¹⁸F-Flutemetamol (Tau)
            </button>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Injection Parameters</div>
          <div class="parameter-item">
            <span class="parameter-label">Injected Dose</span>
            <span class="parameter-value" id="dose-value">10mCi</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="5" max="20" step="1" value="10" 
                   onchange="updatePETParameter('dose', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">Uptake Time</span>
            <span class="parameter-value" id="uptake-value">60min</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="30" max="120" step="15" value="60" 
                   onchange="updatePETParameter('uptake', this.value)">
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Acquisition</div>
          <div class="parameter-item">
            <span class="parameter-label">Scan Duration</span>
            <span class="parameter-value" id="duration-value">20min</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="10" max="30" step="5" value="20" 
                   onchange="updatePETParameter('duration', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">Iterations</span>
            <span class="parameter-value" id="iterations-value">3</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="1" max="8" step="1" value="3" 
                   onchange="updatePETParameter('iterations', this.value)">
          </div>
        </div>

        <button class="scan-button" id="pet-scan-btn" onclick="togglePETScan()">
          Start PET Scan
        </button>
      </div>
    </div>

    <!-- Center Panel: PET Scanner Visualization -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">PET Scanner</div>
        <div class="panel-subtitle">Positron detection and coincidence imaging</div>
      </div>
      <div class="panel-content">
        <div class="pet-scanner">
          <div class="scanner-ring">
            <div class="detector-ring"></div>
            <div class="scanner-bore"></div>
            <div class="positron-emission"></div>
          </div>
          <div class="patient-table"></div>
        </div>
        
        <div class="image-display" style="margin-top: 24px; height: 350px;">
          <div class="image-overlay">
            ¹⁸F-FDG PET | Axial | 10mCi | 60min uptake
          </div>
          <div class="activity-scale">
            <div class="scale-item">
              <div class="scale-color" style="background: #FFD700;"></div>
              <span>High</span>
            </div>
            <div class="scale-item">
              <div class="scale-color" style="background: #FFA500;"></div>
              <span>Med</span>
            </div>
            <div class="scale-item">
              <div class="scale-color" style="background: #FF4500;"></div>
              <span>Low</span>
            </div>
            <div class="scale-item">
              <div class="scale-color" style="background: #8b5cf6;"></div>
              <span>BG</span>
            </div>
          </div>
          <div class="pet-image">
            <div class="brain-outline"></div>
            <div class="metabolic-activity"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel: Quantitative Analysis -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">Quantitative Analysis</div>
        <div class="panel-subtitle">SUV measurements and kinetic modeling</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">SUV Measurements</div>
          <div class="parameter-item">
            <span class="parameter-label">SUV Max</span>
            <span class="parameter-value" id="suv-max">8.5</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">SUV Mean</span>
            <span class="parameter-value" id="suv-mean">4.2</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">SUV Peak</span>
            <span class="parameter-value" id="suv-peak">7.1</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Image Quality</div>
          <div class="parameter-item">
            <span class="parameter-label">Noise Level</span>
            <span class="parameter-value" id="noise-display">8%</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Spatial Resolution</span>
            <span class="parameter-value" id="resolution-display">4.5mm</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Sensitivity</span>
            <span class="parameter-value" id="sensitivity-display">12 kcps/MBq</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Radiation Safety</div>
          <div class="parameter-item">
            <span class="parameter-label">Effective Dose</span>
            <span class="parameter-value" id="effective-dose">7.0 mSv</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Decay Correction</span>
            <span class="parameter-value" id="decay-correction">85%</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Half-life Remaining</span>
            <span class="parameter-value" id="halflife-remaining">78%</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Scan Progress</div>
          <div class="parameter-item">
            <span class="parameter-label">Acquisition Time</span>
            <span class="parameter-value" id="scan-time">00:00</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Bed Position</span>
            <span class="parameter-value" id="bed-position">1/7</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Coincidences</span>
            <span class="parameter-value" id="coincidences">0M</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let isScanning = false;
    let currentTracer = 'fdg';
    let scanTimer = null;

    function selectTracer(tracer) {
      currentTracer = tracer;
      
      // Update active button
      document.querySelectorAll('.tracer-btn').forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');
      
      // Update visualization and parameters based on tracer
      updateTracerVisualization(tracer);
    }

    function updateTracerVisualization(tracer) {
      const activity = document.querySelector('.metabolic-activity');
      const overlay = document.querySelector('.image-overlay');
      
      const tracerData = {
        fdg: {
          color: 'radial-gradient(circle, rgba(255,215,0,0.8) 0%, rgba(255,165,0,0.6) 50%, rgba(255,69,0,0.4) 100%)',
          name: '¹⁸F-FDG PET'
        },
        florbetapir: {
          color: 'radial-gradient(circle, rgba(139,92,246,0.8) 0%, rgba(124,58,237,0.6) 50%, rgba(109,40,217,0.4) 100%)',
          name: '¹⁸F-Florbetapir PET'
        },
        flutemetamol: {
          color: 'radial-gradient(circle, rgba(52,211,153,0.8) 0%, rgba(16,185,129,0.6) 50%, rgba(5,150,105,0.4) 100%)',
          name: '¹⁸F-Flutemetamol PET'
        }
      };
      
      if (activity && tracerData[tracer]) {
        activity.style.background = tracerData[tracer].color;
        overlay.textContent = `${tracerData[tracer].name} | Axial | 10mCi | 60min uptake`;
      }
    }

    function updatePETParameter(parameter, value) {
      const displays = {
        dose: 'dose-value',
        uptake: 'uptake-value',
        duration: 'duration-value',
        iterations: 'iterations-value'
      };
      
      const units = {
        dose: 'mCi',
        uptake: 'min',
        duration: 'min',
        iterations: ''
      };
      
      document.getElementById(displays[parameter]).textContent = value + units[parameter];
      
      // Update derived parameters
      updateDerivedParameters();
    }

    function updateDerivedParameters() {
      const dose = parseInt(document.getElementById('dose-value').textContent);
      const uptake = parseInt(document.getElementById('uptake-value').textContent);
      
      // Update effective dose (simplified calculation)
      const effectiveDose = (dose * 0.7).toFixed(1);
      document.getElementById('effective-dose').textContent = effectiveDose + ' mSv';
      
      // Update decay correction based on uptake time
      const decayCorrection = Math.max(50, 100 - (uptake - 60) * 0.5).toFixed(0);
      document.getElementById('decay-correction').textContent = decayCorrection + '%';
      
      // Update half-life remaining (F-18 half-life = 110 min)
      const halflifeRemaining = Math.max(25, 100 * Math.pow(0.5, uptake / 110)).toFixed(0);
      document.getElementById('halflife-remaining').textContent = halflifeRemaining + '%';
    }

    function togglePETScan() {
      const button = document.getElementById('pet-scan-btn');
      
      if (!isScanning) {
        startPETScan();
        button.textContent = 'Stop Scan';
        button.classList.add('scanning');
      } else {
        stopPETScan();
        button.textContent = 'Start PET Scan';
        button.classList.remove('scanning');
      }
    }

    function startPETScan() {
      isScanning = true;
      let seconds = 0;
      let bedPosition = 1;
      
      scanTimer = setInterval(() => {
        seconds++;
        
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        document.getElementById('scan-time').textContent = 
          `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        
        // Update bed position every 3 minutes (simulated)
        if (seconds % 180 === 0 && bedPosition < 7) {
          bedPosition++;
          document.getElementById('bed-position').textContent = `${bedPosition}/7`;
        }
        
        // Update coincidences
        const coincidences = (seconds * 50000).toLocaleString();
        document.getElementById('coincidences').textContent = (coincidences / 1000000).toFixed(1) + 'M';
        
        // Update SUV values with some variation
        const suvMax = (8.5 + (Math.random() - 0.5) * 2).toFixed(1);
        const suvMean = (4.2 + (Math.random() - 0.5) * 1).toFixed(1);
        const suvPeak = (7.1 + (Math.random() - 0.5) * 1.5).toFixed(1);
        
        document.getElementById('suv-max').textContent = suvMax;
        document.getElementById('suv-mean').textContent = suvMean;
        document.getElementById('suv-peak').textContent = suvPeak;
        
      }, 1000);
    }

    function stopPETScan() {
      isScanning = false;
      if (scanTimer) {
        clearInterval(scanTimer);
        scanTimer = null;
      }
      document.getElementById('scan-time').textContent = '00:00';
      document.getElementById('bed-position').textContent = '1/7';
      document.getElementById('coincidences').textContent = '0M';
    }

    function startWholeBodScan() {
      alert('Whole body PET/CT protocols will be available in the next update!');
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      updateDerivedParameters();
      updateTracerVisualization('fdg');
    });
  </script>
</body>
</html>
