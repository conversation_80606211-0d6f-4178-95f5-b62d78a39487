<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Biomedical Measurement System · Smart Mind Maps</title>
  <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@500;600&family=Patrick+Hand&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="../assets/index-d7x91drb.css">
  <style>
    :root{
      --paper:#f6f3ee; --ink:#1d2a39; --accent:#ffef8a; --muted:#8aa0b8;
      --card:#0f1220; --border:#1f2336;
    }
    body{margin:0;background:#0d1222;color:#e6e9ff;font-family: "Patrick Hand", "Caveat", system-ui, -apple-system, Segoe <PERSON>I, Roboto, Arial, sans-serif}
    .container{max-width:1400px;margin:0 auto;padding:22px}
    .top{display:flex;align-items:center;justify-content:space-between;gap:12px}
    .btn{display:inline-block;padding:10px 14px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b}
    .btn:hover{border-color:#3f4580;background:#1c2040}

    /* Napkin board */
    .board{
      position:relative;
      margin:16px 0 24px;
      background:var(--paper);
      color:var(--ink);
      border-radius:22px;
      padding:30px;
      box-shadow:0 18px 50px rgba(0,0,0,.35);
      min-height:820px;
      border:2px dashed rgba(29,42,57,.25);
      overflow:hidden;
    }
    .board .title{display:flex;align-items:center;gap:10px;margin:2px 2px 10px 4px;color:var(--ink)}
    .pen{width:20px;height:20px;border-radius:4px;background:linear-gradient(160deg,#1c2a3a,#274664)}
    .node{
      position:absolute;padding:8px 12px;border-radius:14px;background:#fffdfc;border:2px solid var(--ink);
      filter:url(#squiggle); -webkit-user-select:none; user-select:none;
      box-shadow:0 1px 0 rgba(0,0,0,.06)
    }
    .node.center{font-size:22px;font-weight:700;border-radius:18px;background:linear-gradient(0deg,#fff,#faf8f2)}
    .node h4{margin:0 0 4px 0}
    .tag{display:inline-block;margin:4px 6px 0 0;border:2px solid var(--ink);border-radius:999px;padding:2px 8px;background:#fffef6;filter:url(#squiggle)}
    .hl{background:linear-gradient(transparent 55%, var(--accent) 55% 92%, transparent 92%);padding:0 4px;border-radius:4px}
    svg.lines{position:absolute;inset:0;pointer-events:none}
    .stroke{fill:none;stroke:var(--ink);stroke-width:3.5;stroke-linecap:round;stroke-linejoin:round;filter:url(#squiggle)}
    .thick{stroke-width:5}

    .picker{display:flex;gap:8px;flex-wrap:wrap;margin:14px 0}
    .chip{padding:8px 12px;border-radius:999px;border:1px dashed #3a4375;background:#101735;color:#cfe1ff;cursor:pointer}
    .chip.active{background:#1a2356;border-color:#5a7bff;box-shadow:0 0 0 2px rgba(46,144,250,.3) inset}

    .legend{color:#cfe1ff;opacity:.9;font-size:14px;margin-top:4px}
    .grid{display:grid;grid-template-columns:1fr;gap:18px}
    @media(min-width: 860px){.grid{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="container">
    <div class="top">
      <div>
        <a class="btn" href="../index.html">← Back to Catalog</a>
      </div>
      <div class="legend">Smart Mind Map · Select a lab module to render its napkin-style overview</div>
    </div>

    <div class="picker" id="picker"></div>

    <div class="grid">
      <section class="board" id="board">
        <div class="title"><div class="pen"></div> <strong>Biomedical Measurement System</strong> · <span id="subtitle" style="opacity:.75"></span></div>
        <svg width="0" height="0">
          <filter id="squiggle">
            <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="1" seed="4" result="noise"/>
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="1.25" xChannelSelector="R" yChannelSelector="G"/>
          </filter>
        </svg>
        <svg class="lines" viewBox="0 0 1400 900" preserveAspectRatio="none" id="wires"></svg>
        <div class="node center" id="center" style="left:640px;top:380px">Biomedical Measurement System</div>
        <!-- dynamic nodes injected here -->
      </section>
    </div>
  </div>

  <script>
    // Data model: each module with its experiments grouped in themed branches
    const modules = {
      "SUSTBME01: ECG": {
        branches: {
          "Filters": ["HPF Characteristic Experiment","LPF Characteristic Experiment","BRF Characteristic Experiment"],
          "Amplification": ["Amplifier Experiment"],
          "Practice": ["ECG Simulator Experiment (Optional)","ECG Experiment"]
        },
        doodle: "❤️"
      },
      "SUSTBME02: EMG": {
        branches: {
          "Filters": ["BRF Characteristic Experiment","LPF Characteristic Experiment","HPF Characteristic Experiment"],
          "Rectify & Integrate": ["Half-Wave Rectifier Characteristic Experiment","Integrator Characteristic Experiment"],
          "Gain": ["Gain Amplifier Experiment"],
          "Practice": ["EMG Experiment"]
        },
        doodle: "💪"
      },
      "SUSTBME03: EOG": {
        branches: {
          "Calibration": ["Horizontal & Vertical Electro Circuit Calibration Experiment"],
          "Filters": ["BRF Characteristic Experiment","HPF Characteristic Experiment","LPF Characteristic Experiment"],
          "Amplification": ["Amplifier Experiment"],
          "Practice": ["EOG Experiment"]
        },
        doodle: "👀"
      },
      "SUSTBME04: EEG": {
        branches: {
          "Calibration": ["Pre-Amplifier Calibration Experiment"],
          "Filters": ["BRF Characteristic Experiment","HPF Characteristic Experiment","LPF Characteristic Experiment"],
          "Amplification": ["Amplifier Experiment"],
          "Practice": ["EEG Experiment"]
        },
        doodle: "🧠"
      },
      "SUSTBME05: Blood Pressure": {
        branches: {
          "Sensors": ["Pressure Sensor Calibration Experiment"],
          "Filters": ["HPF1 Characteristic Experiment","LPF Characteristic Experiment","HPF2 & Amplifier Characteristic Experiment"],
          "Rectify": ["Rectifier Characteristic Experiment"],
          "Methods": ["Auscultatory Blood Pressure measurement Experiment","Oscillometric Blood Pressure Measurement Experiment"]
        },
        doodle: "🩺"
      },
      "SUSTBME06: PPG": {
        branches: {
          "Optics": ["Infrared Photocoupler Calibration Experiment"],
          "Filters": ["HPF Characteristic Experiment","4th-order LPF Characteristic Experiment"],
          "Gain/Logic": ["Gain Amplifier Experiment","Amplifier Experiment","Comparator Experiment","Monostable Multivibrator Experiment"],
          "Derivatives": ["Differentiator Experiment"],
          "Practice": ["Photoplethysmogram Measurement Experiment"]
        },
        doodle: "🔦"
      },
      "SUSTBME07: Respiratory Ventilation": {
        branches: {
          "Calibration": ["Differential Amplifier Calibration Experiment"],
          "Filters": ["BRF Characteristic Experiment"],
          "Gain/Logic": ["Amplifier Experiment","Differentiator Experiment","Hysteresis Comparator Experiment","Monostable Multivibrator Experiment"],
          "Practice": ["Respiratory Ventilation Detection Experiment"]
        },
        doodle: "🌬️"
      },
      "SUSTBME08: Pulse Meter": {
        branches: {
          "Calibration": ["Strain Gauge Amplifier Calibration Experiment"],
          "Filters": ["HPF Characteristic Experiment","BRF Characteristic Experiment","LPF Characteristic Experiment"],
          "Gain/Logic": ["Gain Amplifier Experiment","Hysteresis Comparator Experiment","Monostable Multivibrator Experiment"],
          "Practice": ["Pulse Meter Experiment","Arterial Vessel Experiment"]
        },
        doodle: "📈"
      },
      "SUSTBME09: Impedance (avoid with pacemaker)": {
        branches: {
          "Calibration": ["Pre-Amplifier Calibration Experiment"],
          "Oscillator": ["Wien-Bridge Oscillator Experiment"],
          "Filters": ["BRF Characteristic Experiment","HPF Characteristic Experiment","LPF Characteristic Experiment"],
          "Demod/Amplify": ["Demodulator Experiment","Gain Amplifier Experiment"],
          "Practice": ["Impedance Detection Experiment"]
        },
        doodle: "📡"
      },
      "SUSTBME10: Doppler Ultrasound Velocity": {
        branches: {
          "Front-End": ["OSC Experiment","Pre-Amplifier Experiment"],
          "Demod": ["Demodulation Experiment"],
          "Filters/Gain": ["HPF Characteristic Experiment","Amplifier Experiment","LPF Characteristic Experiment"],
          "Logic": ["Comparator Experiment","Monostable Multivibrator Experiment"]
        },
        doodle: "🩸"
      },
      "SUSTBME11: TENS (avoid with pacemaker)": {
        branches: {
          "Timing": ["555 Timer-Astable Experiment"],
          "Transistor": ["Transistor Switch Circuit Experiment","Transistor Bias Circuit Experiment"]
        },
        doodle: "⚡"
      },
      "SUSTBME12: Respiration Flow / Vital Capacity": {
        branches: {
          "Sensing": ["Hall & Differential Experiment"],
          "Conversion": ["Frequency to Voltage Experiment"],
          "Logic/Display": ["Comparator Experiment","AND Gate Experiment","Decade Counter Experiment","Decoder Experiment","7-Segment Experiment"]
        },
        doodle: "🫁"
      }
    };

    const picker = document.getElementById("picker");
    const board = document.getElementById("board");
    const wires = document.getElementById("wires");
    const center = document.getElementById("center");
    const subtitle = document.getElementById("subtitle");

    function clearNodes(){
      [...board.querySelectorAll(".node:not(.center)")].forEach(n=>n.remove());
      wires.innerHTML = "";
    }

    function chip(label){
      const el = document.createElement("button");
      el.className = "chip";
      el.textContent = label;
      el.addEventListener("click", ()=>{
        document.querySelectorAll(".chip").forEach(c=>c.classList.remove("active"));
        el.classList.add("active");
        renderMindmap(label);
      });
      return el;
    }

    // Layout helpers: radial positions
    function polar(cx, cy, r, angleDeg){
      const a = angleDeg * Math.PI/180;
      return [cx + r*Math.cos(a), cy + r*Math.sin(a)];
    }

    function pathCurve(x1,y1,x2,y2,bulge=0.25){
      const dx = x2-x1, dy = y2-y1;
      const mx = (x1+x2)/2, my = (y1+y2)/2;
      const nx = -dy, ny = dx;
      const cx = mx + nx*bulge, cy = my + ny*bulge;
      return `M${x1},${y1} Q${cx},${cy} ${x2},${y2}`;
    }

    function renderMindmap(key){
      clearNodes();
      subtitle.textContent = key;
      const data = modules[key];
      center.textContent = `${data.doodle} ${key}`;

      const cx = 700, cy = 410;
      const firstRadius = 220;
      const secondRadius = 340;

      const bKeys = Object.keys(data.branches);
      bKeys.forEach((bk, i)=>{
        const ang = -50 + (i*(100/(bKeys.length-1 || 1))); // spread around wider canvas
        const [bx, by] = polar(cx, cy, firstRadius, ang);

        // level-1 node
        const n1 = document.createElement("div");
        n1.className = "node";
        n1.style.left = (bx-90)+"px";
        n1.style.top  = (by-22)+"px";
        n1.innerHTML = `<h4 class="hl">${bk}</h4>`;
        board.appendChild(n1);

        // connector center -> branch
        const p1 = pathCurve(cx,cy,bx,by,0.18);
        const line1 = document.createElementNS("http://www.w3.org/2000/svg","path");
        line1.setAttribute("class","stroke thick");
        line1.setAttribute("d", p1);
        wires.appendChild(line1);

        const leaves = data.branches[bk];
        leaves.forEach((lv, j)=>{
          const subAng = ang + (j - (leaves.length-1)/2) * 12;
          const [sx, sy] = polar(cx, cy, secondRadius, subAng);
          const n2 = document.createElement("div");
          n2.className = "node";
          n2.style.left = (sx-110)+"px";
          n2.style.top  = (sy-18)+"px";
          const isExample = /ECG|EEG|EOG|Experiment$|Measurement/.test(lv) && /Experiment/.test(lv);
          n2.innerHTML = isExample ? `<span class="tag">${lv}</span>` : `<div>${lv}</div>`;
          board.appendChild(n2);

          const p2 = pathCurve(bx,by,sx,sy,0.14);
          const line2 = document.createElementNS("http://www.w3.org/2000/svg","path");
          line2.setAttribute("class","stroke");
          line2.setAttribute("d", p2);
          wires.appendChild(line2);
        });
      });
    }

    // Build picker
    Object.keys(modules).forEach(k=> picker.appendChild(chip(k)));
    // Activate first
    const first = picker.querySelector(".chip"); if(first){ first.classList.add("active"); renderMindmap(first.textContent); }
  </script>
</body>
</html>
