<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Biomedical Measurement System — Virtual Lab Platform</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
<style>
  :root{
    --bg:#101828;
    --panel:#0B1220;
    --panel-2:#111A2C;
    --grid:#0A1020;
    --text:#E5E7EB;
    --text-soft:#CBD5E1;
    --muted:#6B7280;
    --accent:#2E90FA;   /* cool blue */
    --teal:#22D3EE;     /* vibrant teal */
    --warn:#F59E0B;     /* orange for warnings */
    --danger:#EF4444;
    --ok:#10B981;
    --card: rgba(255,255,255,0.02);
    --card-b: rgba(255,255,255,0.08);
    --radius:14px;
    --gap:14px;
    --shadow: 0 10px 30px rgba(0,0,0,.45), inset 0 1px 0 rgba(255,255,255,.02);
    --grid-line: rgba(46,144,250,0.15);
    --yellow:#FCD34D;
  }
  *{box-sizing:border-box}
  html,body{height:100%}
  body{
    margin:0;
    background:
      radial-gradient(1200px 800px at 70% -10%, rgba(46,144,250,0.05), transparent 60%),
      radial-gradient(900px 700px at 20% 110%, rgba(34,211,238,0.05), transparent 60%),
      var(--bg);
    color:var(--text);
    font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  }
  .topbar{
    height:56px; padding:0 18px; display:flex; align-items:center; justify-content:space-between;
    border-bottom:1px solid rgba(255,255,255,0.06);
    background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0));
    -webkit-backdrop-filter: blur(6px); backdrop-filter: blur(6px);
  }
  .brand{ display:flex; align-items:center; gap:10px; font-weight:800 }
  .brand-badge{ width:28px; height:28px; border-radius:8px; background:linear-gradient(135deg,var(--accent),var(--teal)); box-shadow: 0 6px 16px rgba(46,144,250,.35) }
  .actions{ display:flex; gap:8px }
  .top-btn{ padding:8px 12px; border-radius:10px; border:1px solid rgba(255,255,255,0.08); background:var(--card); color:var(--text); cursor:pointer }
  .container{
    height: calc(100% - 56px);
    display:grid;
    grid-template-rows: 1fr 1fr; /* Top half hub, bottom half experiment scene */
    gap: var(--gap);
    padding: var(--gap);
  }
  /* SCENE 1: HUB */
  .hub{
    background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
    border:1px solid rgba(255,255,255,0.06); border-radius:var(--radius); box-shadow:var(--shadow);
    display:flex; flex-direction:column; overflow:hidden; min-height:0;
  }
  .hub-header{
    padding:14px 16px; border-bottom:1px solid rgba(255,255,255,0.06);
    display:flex; align-items:center; justify-content:space-between;
  }
  .hub-title{ font-weight:800; letter-spacing:.2px }
  .hub-grid{
    padding:14px; overflow:auto;
    display:grid; grid-template-columns: repeat(6, minmax(220px, 1fr)); gap:12px;
  }
  .card{
    background: var(--card); border:1px solid rgba(255,255,255,0.06);
    border-radius:12px; padding:12px; display:flex; flex-direction:column; gap:10px;
    box-shadow: 0 10px 28px rgba(0,0,0,.25);
    transition:.2s transform,.2s box-shadow,.2s border-color;
    position:relative;
  }
  .card:hover{ transform: translateY(-2px); border-color: rgba(46,144,250,.4); box-shadow: 0 16px 36px rgba(0,0,0,.35) }
  .card.highlight{ outline:2px solid rgba(46,144,250,.65); box-shadow: 0 0 0 6px rgba(46,144,250,.15), 0 16px 36px rgba(0,0,0,.4) }
  .card-head{ display:flex; align-items:center; gap:10px }
  .mod-icon{
    width:34px; height:34px; border-radius:10px; display:grid; place-items:center;
    background: linear-gradient(135deg, rgba(46,144,250,0.2), rgba(34,211,238,0.18));
    border:1px solid rgba(46,144,250,0.35); color:#BFE4FF; font-weight:900;
    text-shadow: 0 0 8px rgba(46,144,250,.4);
  }
  .mod-title{ font-weight:700; line-height:1.2 }
  .progress{
    display:flex; align-items:center; justify-content:space-between;
    background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06);
    padding:8px; border-radius:10px; font-size:12px; color:var(--text-soft);
  }
  .exp-list{
    background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06);
    border-radius:10px; padding:8px; height:96px; overflow:auto; font-size:12px; color:var(--text-soft)
  }
  .start-btn{
    align-self:flex-end; padding:8px 12px; border-radius:10px; font-weight:800; letter-spacing:.3px;
    background: linear-gradient(180deg, #2E90FA, #1C6CD4);
    border:1px solid rgba(255,255,255,0.12); color:white; cursor:pointer;
    display:flex; align-items:center; gap:6px;
    box-shadow: 0 10px 20px rgba(46,144,250,.25), inset 0 -2px 0 rgba(0,0,0,.25);
  }
  .warning{
    position:absolute; top:10px; right:10px; display:flex; align-items:center; gap:6px;
    padding:6px 8px; border-radius:8px; font-size:11px; font-weight:800;
    background: rgba(245,158,11,0.12); color:#FCD34D; border:1px solid rgba(245,158,11,0.35);
    text-shadow: 0 0 8px rgba(252,211,77,.25);
  }
  .cursor{
    position:absolute; width:22px; height:22px; border:2px solid rgba(255,255,255,0.9);
    border-radius:3px; transform:rotate(20deg); box-shadow:0 0 10px rgba(255,255,255,.3);
  }

  /* SCENE 2: EXPERIMENT */
  .experiment{
    background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel-2);
    border:1px solid rgba(255,255,255,0.06); border-radius:var(--radius); box-shadow:var(--shadow);
    display:grid; grid-template-columns: 340px minmax(520px, 1fr) 420px; gap: var(--gap);
    padding: var(--gap);
  }
  .exp-left, .exp-center, .exp-right{
    background: var(--card);
    border:1px solid rgba(255,255,255,0.06);
    border-radius:12px; overflow:hidden; display:flex; flex-direction:column; min-height:0;
  }
  .section-h{ padding:12px 12px; border-bottom:1px solid rgba(255,255,255,0.06); display:flex; align-items:center; justify-content:space-between }
  .crumbs{ font-size:12px; color:var(--text-soft) }
  .title{ font-weight:800 }
  .scroll{ padding:12px; overflow:auto }

  /* Checklist */
  .checklist{ display:flex; flex-direction:column; gap:10px }
  .step{
    display:grid; grid-template-columns: 28px 1fr auto; align-items:center; gap:10px; padding:10px;
    background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06); border-radius:10px;
  }
  .step-icon{ width:28px; height:28px; border-radius:8px; display:grid; place-items:center; background: rgba(255,255,255,0.04) }
  .step.done{ opacity:.6 }
  .step.current{ border-color: rgba(46,144,250,0.45); box-shadow: 0 0 0 3px rgba(46,144,250,0.15); background: linear-gradient(180deg, rgba(46,144,250,0.10), rgba(46,144,250,0.03)) }
  .theory-tab{
    position:sticky; bottom:0; margin-top:10px; background: rgba(46,144,250,0.08);
    border:1px solid rgba(46,144,250,0.35); color:var(--text); border-radius:10px; padding:10px; font-weight:700; display:flex; align-items:center; gap:8px;
    cursor:pointer;
  }
  .formula{
    display:none; margin-top:10px; padding:10px; border-radius:10px; background: rgba(255,255,255,0.03); border:1px solid rgba(255,255,255,0.06); color:var(--text-soft)
  }

  /* Workbench: breadboard */
  .workbench{
    background:
      radial-gradient(900px 400px at 80% 0%, rgba(46,144,250,0.05), transparent 60%),
      linear-gradient(180deg, #0F172A, #0A1020);
    position:relative; flex:1; min-height:0;
  }
  .breadboard{
    position:absolute; left:50%; top:50%; transform:translate(-50%,-50%);
    width:720px; height:260px; border-radius:16px; background:#ECEFF3; color:#111827;
    box-shadow: 0 20px 40px rgba(0,0,0,.4), inset 0 0 0 2px #BFC7D4;
  }
  .bb-grid{
    position:absolute; inset:18px; display:grid; grid-template-rows: repeat(5, 1fr); gap:6px;
  }
  .bb-row{
    background: #F7FAFC; border-radius:10px; box-shadow: inset 0 0 0 1px #D3DAE6;
    position:relative;
  }
  .bb-hole{
    position:absolute; width:6px; height:6px; border-radius:50%; background:#C1C9D6; box-shadow: inset 0 1px 0 #ffffff;
  }
  /* distribute holes as dots */
  .bb-row::before{
    content:""; position:absolute; left:18px; right:18px; top:50%; height:1px; background:#D8DFEB;
    box-shadow:
      0 0 0 #D8DFEB,
      24px 0 0 #D8DFEB, 48px 0 0 #D8DFEB, 72px 0 0 #D8DFEB, 96px 0 0 #D8DFEB,
      120px 0 0 #D8DFEB, 144px 0 0 #D8DFEB, 168px 0 0 #D8DFEB, 192px 0 0 #D8DFEB,
      216px 0 0 #D8DFEB, 240px 0 0 #D8DFEB, 264px 0 0 #D8DFEB, 288px 0 0 #D8DFEB,
      312px 0 0 #D8DFEB, 336px 0 0 #D8DFEB, 360px 0 0 #D8DFEB, 384px 0 0 #D8DFEB,
      408px 0 0 #D8DFEB, 432px 0 0 #D8DFEB, 456px 0 0 #D8DFEB, 480px 0 0 #D8DFEB,
      504px 0 0 #D8DFEB, 528px 0 0 #D8DFEB, 552px 0 0 #D8DFEB, 576px 0 0 #D8DFEB,
      600px 0 0 #D8DFEB, 624px 0 0 #D8DFEB, 648px 0 0 #D8DFEB, 672px 0 0 #D8DFEB;
    transform: translateY(-0.5px);
    opacity:.9;
  }
  /* Components */
  .resistor{
    position:absolute; width:80px; height:14px; border-radius:6px;
    background: linear-gradient(180deg, #D6A374, #A36C3E); box-shadow: inset 0 -2px 0 rgba(0,0,0,.2), 0 4px 10px rgba(0,0,0,.25);
  }
  .band{ position:absolute; top:0; bottom:0; width:6px; background:#3B82F6; left:16px; }
  .band2{ left:30px; background:#1F2937 }
  .band3{ left:44px; background:#F59E0B }
  .cap{
    position:absolute; width:14px; height:22px; border-radius:3px; background: linear-gradient(180deg, #F4F7FA, #CED5DF);
    box-shadow: inset 0 -2px 0 rgba(0,0,0,.15), 0 6px 12px rgba(0,0,0,.25);
  }
  .cap.dragging{
    outline:2px dashed rgba(46,144,250,.7); outline-offset:3px;
    box-shadow: 0 8px 20px rgba(0,0,0,.6), inset 0 1px 0 rgba(255,255,255,.6);
  }
  .wire{
    position:absolute; height:3px; border-radius:2px;
    filter: drop-shadow(0 0 4px rgba(46,144,250,.35));
  }
  .wire.red{ background:#ef4444 }
  .wire.black{ background:#111827 }
  .wire.yellow{ background:#FBBF24 }

  /* Instruments & data */
  .tray{ padding:10px; display:flex; flex-direction:column; gap:10px; overflow:auto }
  .module{
    background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06);
    border-radius:10px; overflow:hidden;
  }
  .module-h{ padding:10px 12px; border-bottom:1px solid rgba(255,255,255,0.06); font-weight:700; display:flex; align-items:center; justify-content:space-between }
  .module-b{ padding:10px 12px }

  /* Component toolbox list */
  .comp-grid{ display:grid; grid-template-columns: repeat(4, 1fr); gap:10px }
  .comp{
    padding:10px; border-radius:10px; background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06);
    display:flex; flex-direction:column; gap:6px; cursor:grab; -webkit-user-select:none; user-select:none;
  }
  .comp .icon{ width:28px; height:28px; border-radius:8px; display:grid; place-items:center; background: linear-gradient(135deg, rgba(46,144,250,0.2), rgba(34,211,238,0.18)); border:1px solid rgba(46,144,250,0.35) }
  .comp .name{ font-size:12px; font-weight:700 }

  /* Function Generator */
  .grid-2{ display:grid; grid-template-columns: 1fr 1fr; gap:10px }
  .field{ background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06); border-radius:8px; padding:8px; font-size:12px }
  .field label{ display:block; color:var(--text-soft); margin-bottom:4px }
  .field input{ width:100%; padding:8px; border-radius:6px; border:1px solid rgba(255,255,255,0.12); background:#0B1220; color:var(--text) }
  .wave-btns{ display:flex; gap:8px; margin-top:6px }
  .wave{ padding:6px 10px; border-radius:8px; border:1px solid rgba(255,255,255,0.1); background: rgba(255,255,255,0.02); color:var(--text); cursor:pointer }
  .wave.active{ background: linear-gradient(180deg, #2E90FA, #1C6CD4); border-color: rgba(255,255,255,0.2) }

  /* Oscilloscope */
  .scope{
    background: var(--grid); border:1px solid rgba(46,144,250,.25); border-radius:10px; overflow:hidden; position:relative; height:180px;
    box-shadow: inset 0 0 40px rgba(46,144,250,0.08);
  }
  .grid-lines{
    position:absolute; inset:0; background-image:
      linear-gradient(to right, var(--grid-line) 1px, transparent 1px),
      linear-gradient(to bottom, var(--grid-line) 1px, transparent 1px);
    background-size: 24px 24px, 24px 24px; opacity:.9; pointer-events:none;
  }
  canvas#osc{ position:absolute; inset:0; width:100%; height:100% }
  .readouts{ display:grid; grid-template-columns: repeat(3, 1fr); gap:10px; margin-top:8px }
  .read{ background: rgba(255,255,255,0.02); border:1px solid rgba(255,255,255,0.06); border-radius:8px; padding:8px }
  .label{ font-size:11px; color:var(--muted) }
  .value{ font-size:16px; font-weight:800; letter-spacing:.3px; color:#FBBF24 } /* yellow for CH1 default */
  .value.teal{ color:#67E8F9 } /* teal for CH2 */

  /* Data table & actions */
  table{ width:100%; border-collapse:collapse; font-size:12px }
  th, td{ border-bottom:1px solid rgba(255,255,255,0.08); padding:8px; text-align:left; color:var(--text-soft) }
  .actions-bar{ display:flex; gap:8px; margin-top:8px }
  .btn{
    padding:10px 12px; border-radius:10px; font-weight:800; letter-spacing:.3px; cursor:pointer;
    border:1px solid rgba(255,255,255,0.12);
    background: linear-gradient(180deg, #2E90FA, #1C6CD4); color:white;
  }
  .btn.secondary{ background: linear-gradient(180deg, #F59E0B, #D97706) }

  @media (max-width: 1400px){
    .hub-grid{ grid-template-columns: repeat(4, minmax(220px, 1fr)) }
    .experiment{ grid-template-columns: 300px 1fr 400px }
    .breadboard{ width:640px }
  }
  @media (max-width: 1100px){
    .hub-grid{ grid-template-columns: repeat(3, minmax(220px, 1fr)) }
    .experiment{ grid-template-columns: 1fr; grid-auto-rows: minmax(260px, auto) }
  }
</style>
</head>
<body>
  <div class="topbar">
    <div class="brand">
      <div class="brand-badge"></div>
      BioMed LMS
      <div style="font-size:12px; color:var(--text-soft); margin-left:8px">Biomedical Measurement System</div>
    </div>
    <div class="actions">
      <a class="top-btn" href="../index.html">Home</a>
    </div>
  </div>

  <div class="container">
    <!-- SCENE 1: HUB (top half) -->
    <section class="hub" aria-label="Virtual Lab Hub">
      <div class="hub-header">
        <div class="hub-title">Biomedical Measurement System - Virtual Lab Hub</div>
        <div style="font-size:12px; color:var(--text-soft)">Select a module to begin</div>
      </div>
      <div class="hub-grid">
        <!-- ECG Module (highlighted, hovered Start) -->
        <div class="card highlight" data-module="SUSTBME01" style="transform:scale(1.02);">
          <div class="card-head">
            <div class="mod-icon">❤</div>
            <div class="mod-title">SUSTBME01: Electrocardiogram (ECG) Module</div>
          </div>
          <div class="progress">
            <div>Experiments Completed: 0/6</div>
            <div style="color:var(--accent); font-weight:800">In Progress</div>
          </div>
          <div class="exp-list">
            1. HPF Characteristic Exp.<br>
            2. Amplifier Exp.<br>
            3. Lead Configurations<br>
            4. Notch Filter Exp.<br>
            5. Motion Artifact Study<br>
            6. Noise & Shielding
          </div>
          <button class="start-btn">▶ Start Lab</button>
          <!-- Cursor hovering the button -->
          <div class="cursor" style="bottom:28px; right:24px;"></div>
        </div>

        <!-- EMG -->
        <div class="card" data-module="SUSTBME02">
          <div class="card-head">
            <div class="mod-icon">💪</div>
            <div class="mod-title">SUSTBME02: Electromyogram (EMG) Module</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/5</div><div>—</div></div>
          <div class="exp-list">1. Differential Amplifier<br>2. MVC Measurement<br>3. Fatigue Analysis</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- EOG -->
        <div class="card" data-module="SUSTBME03">
          <div class="card-head">
            <div class="mod-icon">👁️</div>
            <div class="mod-title">SUSTBME03: Electrooculogram (EOG) Module</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/4</div><div>—</div></div>
          <div class="exp-list">1. Saccade Recording<br>2. Blink Detection</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- EEG -->
        <div class="card" data-module="SUSTBME04">
          <div class="card-head">
            <div class="mod-icon">🧠</div>
            <div class="mod-title">SUSTBME04: Electroencephalogram (EEG) Module</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/6</div><div>—</div></div>
          <div class="exp-list">1. Alpha Rhythm<br>2. ERP Basics</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- Blood Pressure -->
        <div class="card" data-module="SUSTBME05">
          <div class="card-head">
            <div class="mod-icon">🩺</div>
            <div class="mod-title">SUSTBME05: Blood Pressure Module</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/3</div><div>—</div></div>
          <div class="exp-list">1. Oscillometric Method<br>2. Cuff Leak Test</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- PPG / Pulse Oximetry -->
        <div class="card" data-module="SUSTBME07">
          <div class="card-head">
            <div class="mod-icon">🫀</div>
            <div class="mod-title">SUSTBME07: PPG / Pulse Oximetry</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/4</div><div>—</div></div>
          <div class="exp-list">1. PPG Basics<br>2. SpO₂ Estimation</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- Temperature / Thermistor -->
        <div class="card" data-module="SUSTBME08">
          <div class="card-head">
            <div class="mod-icon">🌡️</div>
            <div class="mod-title">SUSTBME08: Temperature / Thermistor</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/3</div><div>—</div></div>
          <div class="exp-list">1. Thermistor Calibration<br>2. Linearization</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- Impedance with Safety Warning -->
        <div class="card" data-module="SUSTBME09">
          <div class="warning">⚠️ Safety Warning: Not for users with cardiac pacemakers.</div>
          <div class="card-head">
            <div class="mod-icon">Ω</div>
            <div class="mod-title">SUSTBME09: Impedance Module</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/5</div><div>—</div></div>
          <div class="exp-list">1. Bio-impedance Basics<br>2. Cole Plot</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- GSR -->
        <div class="card" data-module="SUSTBME10">
          <div class="card-head">
            <div class="mod-icon">🖐️</div>
            <div class="mod-title">SUSTBME10: GSR (Galvanic Skin Response)</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/3</div><div>—</div></div>
          <div class="exp-list">1. Tonic/Phasic Response<br>2. Stimulus Response</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- TENS with Safety Warning -->
        <div class="card" data-module="SUSTBME11">
          <div class="warning">⚠️ Safety Warning: Not for users with cardiac pacemakers.</div>
          <div class="card-head">
            <div class="mod-icon">⚡</div>
            <div class="mod-title">SUSTBME11: TENS Module</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/4</div><div>—</div></div>
          <div class="exp-list">1. Pulse Parameters<br>2. Sensory Threshold</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>

        <!-- NIBP / ABP Simulator Integration -->
        <div class="card" data-module="SUSTBME12">
          <div class="card-head">
            <div class="mod-icon">🫀</div>
            <div class="mod-title">SUSTBME12: NIBP / ABP Simulator Integration</div>
          </div>
          <div class="progress"><div>Experiments Completed: 0/3</div><div>—</div></div>
          <div class="exp-list">1. NIBP Cuff Simulation<br>2. ABP Waveform Playback</div>
          <button class="start-btn">▶ Start Lab</button>
        </div>
      </div>
    </section>

    <!-- SCENE 2: EXPERIMENT (bottom half) -->
    <section class="experiment" id="sceneExp" aria-label="SUSTBME01 > Exp 1: HPF Characteristic">
      <!-- Left: Guided Procedure & Theory -->
      <div class="exp-left">
        <div class="section-h">
          <div class="title">Guided Procedure</div>
          <div class="crumbs" id="expCrumbs">SUSTBME01: ECG > Exp 1: HPF Characteristic</div>
        </div>
        <div class="scroll">
          <div class="checklist">
            <div class="step done">
              <div class="step-icon">R</div>
              <div>1. Place 1kΩ Resistor (R1) on Breadboard.</div>
              <div style="font-size:12px; color:var(--ok);">✔</div>
            </div>
            <div class="step current">
              <div class="step-icon">C</div>
              <div>2. Place 0.1µF Capacitor (C1) on Breadboard.</div>
              <div style="font-size:12px; color:var(--accent)">▶</div>
            </div>
            <div class="step">
              <div class="step-icon">FG</div>
              <div>3. Connect Function Generator to Circuit Input.</div>
              <div style="font-size:12px;">—</div>
            </div>
            <div class="step">
              <div class="step-icon">OSC</div>
              <div>4. Connect Oscilloscope CH1 to Input, CH2 to Output.</div>
              <div style="font-size:12px;">—</div>
            </div>
            <div class="step">
              <div class="step-icon">∿</div>
              <div>5. Set Function Generator to 1Vp-p Sine Wave.</div>
              <div style="font-size:12px;">—</div>
            </div>
            <div class="step">
              <div class="step-icon">Σ</div>
              <div>6. Sweep Frequency and Record V_out.</div>
              <div style="font-size:12px;">—</div>
            </div>
          </div>

          <div class="theory-tab" id="theoryTab">
            <div class="mod-icon" style="width:24px;height:24px">ƒx</div>
            <div>(Formulas) Show/Hide</div>
          </div>
          <div class="formula" id="formulaBox">
            Cutoff Frequency (fc) = 1 / (2πRC)
          </div>
        </div>
      </div>

      <!-- Center: Workbench -->
      <div class="exp-center">
        <div class="section-h">
          <div class="title" id="workbenchTitle">Modular Virtual Workbench</div>
          <div class="crumbs" id="workbenchCrumbs">Breadboard • Circuit Assembly</div>
        </div>
        <div class="workbench" id="workbenchRoot">
          <div class="breadboard" id="breadboard" aria-label="Virtual Breadboard (click to place components)">
            <div class="bb-grid">
              <div class="bb-row"></div>
              <div class="bb-row"></div>
              <div class="bb-row"></div>
              <div class="bb-row"></div>
              <div class="bb-row"></div>
            </div>

            <!-- R1 initially placed (can be moved) -->
            <div class="resistor" id="R1" data-type="resistor" title="R1: 1kΩ" style="left:200px; top:110px">
              <div class="band"></div><div class="band2"></div><div class="band3"></div>
            </div>

            <!-- C1 unplaced initially; appears following cursor until placed -->
            <div class="cap dragging" id="C1" data-type="capacitor" title="C1: 0.1µF" style="left:320px; top:96px"></div>
            <div class="cursor" id="ghostCursor" style="left:338px; top:104px;"></div>

            <!-- Wires: power (red), ground (black), signal (yellow) -->
            <div class="wire red" style="left:80px; top:70px; width:140px"></div>
            <div class="wire black" style="left:80px; top:200px; width:160px"></div>
            <div class="wire yellow" style="left:260px; top:140px; width:160px"></div>
          </div>
          <div style="position:absolute; left:12px; bottom:12px; font-size:12px; color:#94A3B8; background:rgba(0,0,0,.35); padding:6px 8px; border-radius:8px; border:1px solid rgba(255,255,255,0.06)">
            Click on the breadboard grid to place C1 (snap enabled). Shift+Click a placed part to pick it up again.
          </div>
        </div>
      </div>

      <!-- Right: Instruments & Data -->
      <div class="exp-right">
        <div class="section-h">
          <div class="title">Instrumentation, Tools & Data</div>
          <div class="crumbs" id="instCrumbs">Sine selected • 1Vp-p</div>
        </div>
        <div class="tray">
          <!-- A. Components -->
          <div class="module">
            <div class="module-h">Components</div>
            <div class="module-b">
              <div class="comp-grid">
                <div class="comp" title="Resistor 1kΩ (highlighted)" style="outline:2px solid rgba(46,144,250,.6)">
                  <div class="icon">R</div>
                  <div class="name">Resistor</div>
                  <div style="font-size:12px; color:var(--text-soft)">1kΩ</div>
                </div>
                <div class="comp" title="Capacitor 0.1µF (highlighted)" style="outline:2px solid rgba(46,144,250,.6)">
                  <div class="icon">C</div>
                  <div class="name">Capacitor</div>
                  <div style="font-size:12px; color:var(--text-soft)">0.1µF</div>
                </div>
                <div class="comp" title="Op-Amp">
                  <div class="icon">A</div>
                  <div class="name">Op-Amp</div>
                </div>
                <div class="comp" title="Wires">
                  <div class="icon">W</div>
                  <div class="name">Wires</div>
                </div>
              </div>
            </div>
          </div>

          <!-- B. Virtual Instruments -->
          <div class="module">
            <div class="module-h">Function Generator</div>
            <div class="module-b">
              <div class="grid-2">
                <div class="field">
                  <label>Frequency (Hz)</label>
                  <input id="freq" type="number" value="1500" aria-label="Function Generator Frequency in Hertz" placeholder="1500">
                </div>
                <div class="field">
                  <label>Amplitude (Vp-p)</label>
                  <input id="amp" type="number" value="1.00" step="0.01" aria-label="Function Generator Amplitude in Volts peak-to-peak" placeholder="1.00">
                </div>
              </div>
              <div class="wave-btns">
                <button class="wave active" id="w-sine">Sine</button>
                <button class="wave" id="w-square">Square</button>
                <button class="wave" id="w-tri">Triangle</button>
              </div>
            </div>
          </div>

          <div class="module">
            <div class="module-h">Oscilloscope</div>
            <div class="module-b">
              <div class="scope">
                <div class="grid-lines"></div>
                <canvas id="osc"></canvas>
              </div>
              <div class="readouts">
                <div class="read"><div class="label">CH1 Vp-p</div><div class="value" id="ch1">1.00 V</div></div>
                <div class="read"><div class="label">CH2 Vp-p</div><div class="value teal" id="ch2">0.85 V</div></div>
                <div class="read"><div class="label">Frequency</div><div class="value" id="freqRead">1.5 kHz</div></div>
              </div>
            </div>
          </div>

          <!-- C. Data & Reporting -->
          <div class="module">
            <div class="module-h">Results & Lab Report</div>
            <div class="module-b">
              <div style="overflow:auto; max-height:160px;">
                <table>
                  <thead><tr><th>Frequency (Hz)</th><th>V_out (V)</th><th>Gain (dB)</th></tr></thead>
                  <tbody id="dataRows">
                    <tr><td>500</td><td>0.42</td><td>-7.5</td></tr>
                    <tr><td>1000</td><td>0.70</td><td>-3.1</td></tr>
                    <tr><td>1500</td><td>0.85</td><td>-1.4</td></tr>
                  </tbody>
                </table>
              </div>
              <div class="actions-bar">
                <button class="btn secondary" id="plotBtn">Plot Data</button>
                <button class="btn" id="reportBtn">Generate Lab Report</button>
              </div>
            </div>
          </div>

        </div>
      </div>
    </section>
  </div>

<script>
  // Module router: add placeholders for SUSTBME01–SUSTBME09 and fully implement SUSTBME03: EOG — Saccade Recording

  const hub = document.querySelector('.hub-grid');
  const sceneExp = document.getElementById('sceneExp');
  const expCrumbs = document.getElementById('expCrumbs');
  const workbenchTitle = document.getElementById('workbenchTitle');
  const workbenchCrumbs = document.getElementById('workbenchCrumbs');
  const workbenchRoot = document.getElementById('workbenchRoot');
  const instCrumbs = document.getElementById('instCrumbs');

  // Register Start buttons to switch scene content
  hub.querySelectorAll('.card .start-btn').forEach(btn=>{
    btn.addEventListener('click', (e)=>{
      const card = e.currentTarget.closest('.card');
      const modTitle = card.querySelector('.mod-title')?.textContent?.trim() || 'Module';
      const code = card.getAttribute('data-module') || '';
      if (/SUSTBME03/i.test(code)) {
        loadEOGSaccade();
      } else if (/SUSTBME01/i.test(code)) {
        loadHPFScene(); // load the existing HPF breadboard scene
      } else {
        loadPlaceholder(modTitle);
      }
      sceneExp.scrollIntoView({behavior:'smooth', block:'center'});
    });
  });

  function clearWorkbench(){
    workbenchRoot.innerHTML = '';
  }

  // Reuse existing HPF breadboard scene (SUSTBME01)
  function loadHPFScene(){
    clearWorkbench();
    expCrumbs.textContent = 'SUSTBME01: ECG > Exp 1: HPF Characteristic';
    workbenchTitle.textContent = 'Modular Virtual Workbench';
    workbenchCrumbs.textContent = 'Breadboard • Circuit Assembly';
    instCrumbs.textContent = 'Sine selected • 1Vp-p';

    const html = `
      <div class="breadboard" id="breadboard" aria-label="Virtual Breadboard (click to place components)">
        <div class="bb-grid">
          <div class="bb-row"></div>
          <div class="bb-row"></div>
          <div class="bb-row"></div>
          <div class="bb-row"></div>
          <div class="bb-row"></div>
        </div>
        <div class="resistor" id="R1" data-type="resistor" title="R1: 1kΩ" style="left:200px; top:110px">
          <div class="band"></div><div class="band2"></div><div class="band3"></div>
        </div>
        <div class="cap dragging" id="C1" data-type="capacitor" title="C1: 0.1µF" style="left:320px; top:96px"></div>
        <div class="cursor" id="ghostCursor" style="left:338px; top:104px;"></div>
        <div class="wire red" style="left:80px; top:70px; width:140px"></div>
        <div class="wire black" style="left:80px; top:200px; width:160px"></div>
        <div class="wire yellow" style="left:260px; top:140px; width:160px"></div>
      </div>
      <div style="position:absolute; left:12px; bottom:12px; font-size:12px; color:#94A3B8; background:rgba(0,0,0,.35); padding:6px 8px; border-radius:8px; border:1px solid rgba(255,255,255,0.06)">
        Click on the breadboard grid to place C1 (snap enabled). Shift+Click a placed part to pick it up again.
      </div>
    `;
    const wrap = document.createElement('div');
    wrap.style.cssText = 'position:relative;flex:1;min-height:0;';
    wrap.innerHTML = html;
    workbenchRoot.appendChild(wrap);

    // re-bind HPF placement script
    bindHPFPlacement();
  }

  function loadPlaceholder(moduleTitle){
    clearWorkbench();
    expCrumbs.textContent = `${moduleTitle} > Placeholder Scene`;
    workbenchTitle.textContent = 'Experiment Workbench (Placeholder)';
    workbenchCrumbs.textContent = 'Coming Soon • Interactive setup and instruments';
    instCrumbs.textContent = 'UI scaffold only';

    const wrap = document.createElement('div');
    wrap.style.cssText = 'display:grid;place-items:center;min-height:220px;color:#94A3B8';
    wrap.innerHTML = `
      <div style="text-align:center;max-width:720px">
        <div style="font-size:18px;font-weight:800;color:#E5E7EB;margin-bottom:6px">Experiment Scenes Coming Soon</div>
        <div style="font-size:13px">This module will include a guided checklist, virtual workbench, instruments, and data logging tools. Please select SUSTBME03 to view a fully implemented example (EOG — Saccade Recording).</div>
      </div>`;
    workbenchRoot.appendChild(wrap);
  }

  // Fully-implemented EOG: Saccade Recording (SUSTBME03)
  function loadEOGSaccade(){
    clearWorkbench();
    expCrumbs.textContent = 'SUSTBME03: EOG > Exp 1: Saccade Recording';
    workbenchTitle.textContent = 'Virtual Eye & Electrode Placement';
    workbenchCrumbs.textContent = 'Horizontal Saccade Task • Electrode montage';
    instCrumbs.textContent = 'Task: 10° left/right saccades • Sampling 250 Hz';

    // Build EOG scene
    // Center: eye model and electrodes
    const eye = document.createElement('div');
    eye.style.cssText = 'position:relative;flex:1;min-height:0;';
    eye.innerHTML = `
      <div style="position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:520px;height:240px;border-radius:18px;background:linear-gradient(180deg,#0E1527,#0A1020);box-shadow: inset 0 0 0 1px rgba(255,255,255,.06), 0 20px 40px rgba(0,0,0,.4);">
        <!-- head silhouette -->
        <div style="position:absolute;left:50%;top:52%;transform:translate(-50%,-50%);width:360px;height:160px;border-radius:80px;background:linear-gradient(180deg,#1A2236,#0E1527 60%,#0B1220);box-shadow: inset 0 -20px 40px rgba(0,0,0,.35), inset 0 20px 40px rgba(255,255,255,.04);"></div>
        <!-- eye -->
        <div id="eyeBall" style="position:absolute;left:50%;top:52%;transform:translate(-50%,-50%);width:120px;height:120px;border-radius:50%;background:radial-gradient(circle at 40% 40%, #FFFFFF, #D1D5DB 60%, #9CA3AF);box-shadow: inset 0 0 40px rgba(0,0,0,.2), 0 8px 20px rgba(0,0,0,.35);"></div>
        <!-- pupil -->
        <div id="pupil" style="position:absolute;left:50%;top:52%;transform:translate(-50%,-50%);width:28px;height:28px;border-radius:50%;background:#111827;box-shadow:0 0 12px rgba(0,0,0,.5);"></div>

        <!-- electrodes at canthi -->
        <div id="elecL" title="Left Canthus Electrode" style="position:absolute;left:calc(50% - 110px);top:52%;transform:translate(-50%,-50%);width:18px;height:18px;border-radius:50%;background:radial-gradient(circle at 35% 35%, #F3F4F6, #9CA3AF 65%, #6B7280);border:2px solid #374151;box-shadow:0 4px 8px rgba(0,0,0,.4), inset 0 1px 0 rgba(255,255,255,.6)"></div>
        <div id="elecR" title="Right Canthus Electrode" style="position:absolute;left:calc(50% + 110px);top:52%;transform:translate(-50%,-50%);width:18px;height:18px;border-radius:50%;background:radial-gradient(circle at 35% 35%, #F3F4F6, #9CA3AF 65%, #6B7280);border:2px solid #374151;box-shadow:0 4px 8px rgba(0,0,0,.4), inset 0 1px 0 rgba(255,255,255,.6)"></div>

        <!-- targets -->
        <div id="tLeft"  style="position:absolute;left:20px; top:12px;color:#94A3B8;font-size:12px;">◀ Target</div>
        <div id="tRight" style="position:absolute;right:20px; top:12px;color:#94A3B8;font-size:12px;">Target ▶</div>
      </div>
    `;
    workbenchRoot.appendChild(eye);

    // Right-side instruments already present; we reuse the existing oscilloscope but switch to EOG colors
    // Update readouts to reflect EOG units
    document.getElementById('ch1').textContent = '200 µV';
    document.getElementById('ch2').textContent = '—';
    document.getElementById('freqRead').textContent = 'Task: 1 saccade/s';

    // Drive EOG-like horizontal saccade waveform on existing oscilloscope
    (function EOGWave(){
      const canvas = document.getElementById('osc');
      const ctx = canvas.getContext('2d');
      function fit(){ const dpr=window.devicePixelRatio||1; const r=canvas.getBoundingClientRect(); canvas.width=r.width*dpr; canvas.height=r.height*dpr; ctx.setTransform(dpr,0,0,dpr,0,0); }
      fit();
      window.addEventListener('resize', fit);
      let t=0;
      function draw(){
        const r=canvas.getBoundingClientRect(), w=r.width, h=r.height;
        ctx.clearRect(0,0,w,h);
        const samples=800;
        // EOG CH1: horizontal saccade step-like deflection (yellow)
        ctx.lineWidth=2;
        ctx.strokeStyle='#FBBF24';
        ctx.beginPath();
        for(let i=0;i<samples;i++){
          const x=i/(samples-1)*w;
          const phase=((i+t)%samples)/samples;
          // build a pseudo step: low baseline, quick step up, hold, return
          let yval=0;
          if(phase<0.1){ yval = 0.0; }
          else if(phase<0.15){ yval = (phase-0.1)/0.05; } // step up
          else if(phase<0.45){ yval = 1.0; }              // hold
          else if(phase<0.50){ yval = 1.0 - (phase-0.45)/0.05; } // fall
          else { yval = 0.0; }
          const y = h*0.6 - yval * (h*0.25);
          if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
        }
        ctx.stroke();

        // CH2 dashed teal (optional filtered/derived) — keep blank or faint
        ctx.setLineDash([8,6]);
        ctx.strokeStyle='rgba(103,232,249,0.5)';
        ctx.beginPath();
        for(let i=0;i<samples;i++){
          const x=i/(samples-1)*w;
          const phase=((i+t*1.02)%samples)/samples;
          const y = h*0.6 - 0.2 * Math.sin(phase*Math.PI*2*2) * (h*0.15);
          if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
        }
        ctx.stroke();
        ctx.setLineDash([]);

        t=(t+2)%samples;
        requestAnimationFrame(draw);
      }
      requestAnimationFrame(draw);
    })();
  }

  // Theory toggle
  const theoryTab = document.getElementById('theoryTab');
  const formulaBox = document.getElementById('formulaBox');
  theoryTab.addEventListener('click', () => {
    const v = getComputedStyle(formulaBox).display;
    formulaBox.style.display = v === 'none' ? 'block' : 'none';
  });

  // Simple oscilloscope rendering for CH1 (yellow solid sine) and CH2 (teal dashed attenuated)
  function fitCanvas(canvas){
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = Math.round(rect.width * dpr);
    canvas.height = Math.round(rect.height * dpr);
    const ctx = canvas.getContext('2d');
    ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
    return ctx;
  }
  const osc = document.getElementById('osc');
  let octx = fitCanvas(osc);
  let phase = 0;
  let freqHz = 1500;
  let ampVp = 1.0;
  function drawOsc(ts){
    const rect = osc.getBoundingClientRect();
    const w = rect.width, h = rect.height;
    octx.clearRect(0,0,w,h);

    // axes grid lines are CSS; draw traces
    const samples = 800;
    const ch1Color = '#FBBF24'; // yellow
    const ch2Color = '#67E8F9'; // teal
    const amp1 = h*0.18 * (ampVp / 1.0);
    const amp2 = amp1 * 0.85; // attenuated HPF output example

    // Sine only for now
    octx.lineWidth = 2;

    // CH1 solid
    octx.strokeStyle = ch1Color;
    octx.beginPath();
    for(let i=0;i<samples;i++){
      const x = i / (samples-1) * w;
      const t = (i + phase) / samples;
      const y = h*0.5 - Math.sin(t* Math.PI * 4) * amp1; // 2 cycles
      if(i===0) octx.moveTo(x,y); else octx.lineTo(x,y);
    }
    octx.stroke();

    // CH2 dashed
    octx.setLineDash([8,6]);
    octx.strokeStyle = ch2Color;
    octx.beginPath();
    for(let i=0;i<samples;i++){
      const x = i / (samples-1) * w;
      const t = (i + phase*1.02) / samples;
      const y = h*0.5 - Math.sin(t* Math.PI * 4) * amp2;
      if(i===0) octx.moveTo(x,y); else octx.lineTo(x,y);
    }
    octx.stroke();
    octx.setLineDash([]);

    phase = (phase + 2) % samples;
    requestAnimationFrame(drawOsc);
  }
  requestAnimationFrame(drawOsc);

  // Inputs reflect into readouts (simple demo)
  const freqInput = document.getElementById('freq');
  const ampInput = document.getElementById('amp');
  const freqRead = document.getElementById('freqRead');
  const ch1 = document.getElementById('ch1');
  const ch2 = document.getElementById('ch2');

  function updateReads(){
    const f = Number(freqInput.value||1500);
    const a = Number(ampInput.value||1.0);
    freqRead.textContent = (f>=1000 ? (f/1000).toFixed(1)+' kHz' : f+' Hz');
    ch1.textContent = a.toFixed(2) + ' V';
    ch2.textContent = (a*0.85).toFixed(2) + ' V';
    freqHz = f; ampVp = a;
  }
  freqInput.addEventListener('input', updateReads);
  ampInput.addEventListener('input', updateReads);
  updateReads();

  // Wave select buttons visual state
  const ws = {sine: document.getElementById('w-sine'), square: document.getElementById('w-square'), tri: document.getElementById('w-tri')};
  Object.values(ws).forEach(btn=>{
    btn.addEventListener('click', ()=>{
      Object.values(ws).forEach(b=> b.classList.remove('active'));
      btn.classList.add('active');
    });
  });

  // Plot and Report placeholders
  document.getElementById('plotBtn').addEventListener('click', ()=>{
    alert('Plot Data: would generate a Bode plot (Gain vs Frequency) from the table entries.');
  });
  document.getElementById('reportBtn').addEventListener('click', ()=>{
    alert('Generate Lab Report: would compile procedures, circuit diagram, measurements, and plots into a single document.');
  });

  // Handle resize
  window.addEventListener('resize', ()=>{ octx = fitCanvas(osc); });
  // --- Interactive placement: snap-to-holes for R and C ---
  function bindHPFPlacement(){
    const bb = document.getElementById('breadboard');
    if(!bb) return;
    const C1 = document.getElementById('C1');
    const R1 = document.getElementById('R1');
    const ghostCursor = document.getElementById('ghostCursor');

    function getSnap(x, y){
      const rect = bb.getBoundingClientRect();
      const inset = 18;
      const width = rect.width - inset*2;
      const height = rect.height - inset*2;
      const stepX = 24;
      const cols = Math.round(width / stepX);
      const localX = x - rect.left, localY = y - rect.top;
      const snappedCol = Math.max(0, Math.min(cols, Math.round((localX - inset) / stepX)));
      const snapX = inset + snappedCol * stepX;
      const rows = 5; const rowStep = height / (rows);
      const snappedRow = Math.max(0, Math.min(rows-1, Math.round((localY - inset - rowStep/2) / rowStep)));
      const snapY = inset + rowStep/2 + snappedRow * rowStep;
      return { x: snapX, y: snapY };
    }
    let placing = { target: C1, following: true };
    function moveGhostCursor(e){
      const rect = bb.getBoundingClientRect();
      ghostCursor.style.left = (e.clientX - rect.left + 8) + 'px';
      ghostCursor.style.top  = (e.clientY - rect.top  + 8) + 'px';
    }
    function onMouseMove(e){
      if(!placing.following) return;
      const { x, y } = getSnap(e.clientX, e.clientY);
      placing.target.style.left = (x) + 'px';
      placing.target.style.top  = (y - 12) + 'px';
      moveGhostCursor(e);
    }
    function onClickPlace(e){
      const { x, y } = getSnap(e.clientX, e.clientY);
      placing.target.style.left = (x) + 'px';
      placing.target.style.top  = (y - 12) + 'px';
      placing.target.classList.remove('dragging');
      placing.following = false;
      ghostCursor.style.display = 'none';
    }
    function tryPickup(e){
      const t = e.target.closest('[data-type]');
      if(!t || !e.shiftKey) return;
      e.preventDefault();
      placing = { target: t, following: true };
      t.classList.add('dragging');
      ghostCursor.style.display = 'block';
    }
    R1?.addEventListener('mousedown', tryPickup);
    C1?.addEventListener('mousedown', tryPickup);
    bb.addEventListener('mousemove', onMouseMove);
    bb.addEventListener('click', onClickPlace);
    ghostCursor.style.display = 'block';
  }

  // Bind on initial load if HPF scene present
  (function(){
    const bb = document.getElementById('breadboard');
    if(!bb) return; // not on HPF scene initially after routing
    const C1 = document.getElementById('C1');
    const R1 = document.getElementById('R1');
    const ghostCursor = document.getElementById('ghostCursor');

    // Breadboard grid parameters (aligned with CSS .bb-grid/.bb-row::before spacing)
    // We will derive a simple snap grid over the breadboard bounding box.
    function getSnap(x, y){
      const rect = bb.getBoundingClientRect();
      const localX = x - rect.left;
      const localY = y - rect.top;

      // inner padding approximates .bb-grid inset:18px; rows gap:6px; 5 rows -> we snap to 5 lanes
      const inset = 18;
      const width = rect.width - inset*2;
      const height = rect.height - inset*2;

      // horizontal columns: approximate 28 columns with ~24px step (as .bb-row::before uses multiples of 24px)
      const stepX = 24;
      const cols = Math.round(width / stepX);
      const snappedCol = Math.max(0, Math.min(cols, Math.round((localX - inset) / stepX)));
      const snapX = inset + snappedCol * stepX;

      // vertical rows: 5 rows with equal spacing in bb-grid; compute nearest row center
      const rows = 5;
      const rowStep = height / (rows);
      // snap to row center lines
      const snappedRow = Math.max(0, Math.min(rows-1, Math.round((localY - inset - rowStep/2) / rowStep)));
      const snapY = inset + rowStep/2 + snappedRow * rowStep;

      return { x: snapX, y: snapY };
    }

    let placing = { target: C1, following: true };
    let pickedOffset = { dx: 0, dy: 0 };

    function moveGhostCursor(e){
      const rect = bb.getBoundingClientRect();
      ghostCursor.style.left = (e.clientX - rect.left + 8) + 'px';
      ghostCursor.style.top  = (e.clientY - rect.top  + 8) + 'px';
    }

    function onMouseMove(e){
      if(!placing.following) return;
      const { x, y } = getSnap(e.clientX, e.clientY);
      placing.target.style.left = (x) + 'px';
      placing.target.style.top  = (y - 12) + 'px'; // center vertically for component height
      moveGhostCursor(e);
    }

    function onClickPlace(e){
      // Place current target at snapped location
      const { x, y } = getSnap(e.clientX, e.clientY);
      placing.target.style.left = (x) + 'px';
      placing.target.style.top  = (y - 12) + 'px';
      placing.target.classList.remove('dragging');
      placing.following = false;
      ghostCursor.style.display = 'none';
    }

    // Pick-up logic with Shift+Click on a component
    function tryPickup(e){
      const t = e.target.closest('[data-type]');
      if(!t) return;
      if(!e.shiftKey) return;
      e.preventDefault();
      placing = { target: t, following: true };
      t.classList.add('dragging');
      ghostCursor.style.display = 'block';
    }

    // Allow R1 to be repositioned via Shift+Click
    R1.addEventListener('mousedown', tryPickup);
    C1.addEventListener('mousedown', tryPickup);

    // Breadboard listeners
    bb.addEventListener('mousemove', onMouseMove);
    bb.addEventListener('click', onClickPlace);

    // Initialize: show ghost cursor while placing C1 initially
    ghostCursor.style.display = 'block';
  })();
</script>
</body>
</html>
