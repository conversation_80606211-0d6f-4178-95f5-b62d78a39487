<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Biomedical Engineering · Napkin Mind Map</title>
  <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@500;600&family=Patrick+Hand&display=swap" rel="stylesheet">
  <style>
    :root{
      --paper:#f6f3ee;            /* off‑white napkin */
      --ink:#1d2a39;              /* blue/black ink */
      --accent:#ffef8a;           /* soft yellow highlighter */
      --teal:#79c8c7;             /* muted teal accent (sparingly) */
    }
    html,body{height:100%}
    body{
      margin:0;background:var(--paper);
      background-image:
        radial-gradient(1200px 700px at 10% -10%, #ffffff 0%, #f7f5f1 50%, #f1eee8 100%),
        repeating-linear-gradient(0deg, rgba(0,0,0,.015) 0 2px, transparent 2px 6px);
      color:var(--ink);
      font-family: "Patrick Hand", "Caveat", system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
    }
    .wrap{max-width:1400px;margin:0 auto;padding:28px}
    .board{
      position:relative;
      border:2px dashed rgba(29,42,57,.25);
      border-radius:22px;
      padding:30px;
      background:
        radial-gradient(circle at 20% 10%, rgba(0,0,0,.035), transparent 35%),
        radial-gradient(circle at 80% 90%, rgba(0,0,0,.02), transparent 40%);
      box-shadow:
        0 10px 30px rgba(0,0,0,.06),
        inset 0 1px 0 rgba(255,255,255,.6);
      min-height:820px;
      overflow:hidden;
    }
    .title{
      display:flex;align-items:center;gap:10px;margin:0 0 10px 4px;
      font-size:20px;letter-spacing:.4px
    }
    .pen{width:22px;height:22px;border-radius:4px;background:linear-gradient(160deg,#1c2a3a,#274664)}
    /* Hand‑drawn box */
    .node{
      position:absolute; padding:10px 14px; border-radius:14px;
      background:rgba(255,255,255,.75);
      box-shadow: 0 2px 0 rgba(0,0,0,.06);
      border:2px solid var(--ink);
      transform: rotate(var(--rot, 0deg));
      filter: url(#squiggle);
      -webkit-user-select:none; user-select:none;
    }
    .node.center{
      padding:16px 20px;border-radius:20px;font-size:28px;font-weight:600;
      background:linear-gradient(0deg, #fff 0%, #faf8f3 100%);
      box-shadow: 0 8px 24px rgba(0,0,0,.07);
    }
    .node h3{margin:0;font-weight:600}
    .ink{color:var(--ink)}
    .icon{
      display:inline-block;margin-right:8px;vertical-align:middle;
      filter: url(#squiggle);
    }
    /* Highlighter */
    .hl{
      background: linear-gradient(transparent 55%, var(--accent) 55% 92%, transparent 92%);
      padding:0 4px;border-radius:4px;
    }
    /* Small leaf tags */
    .leaf{
      display:inline-block;margin-top:6px;margin-right:6px;padding:4px 8px;border-radius:999px;
      border:2px solid var(--ink); background:#fffdf5; transform: rotate(var(--rot, 0deg));
      filter: url(#squiggle);
    }
    /* Hand‑drawn connectors */
    svg.lines{position:absolute;inset:0;pointer-events:none}
    .stroke{
      fill:none; stroke:var(--ink); stroke-width:3.5; stroke-linecap:round; stroke-linejoin:round;
      filter:url(#squiggle);
    }
    .thick{stroke-width:5}
    /* Legend / footer */
    .legend{margin-top:14px;color:#2c3a4d;opacity:.8;font-size:15px}
    .legend span.badge{
      display:inline-block;border:2px solid var(--ink);border-radius:10px;padding:4px 8px;margin-right:8px;background:#fffef3;filter:url(#squiggle)
    }
    /* Responsive tweak */
    @media (max-width: 780px){
      .board{min-height:880px}
    }
  </style>
</head>
<body>
  <div class="wrap">
    <div class="title"><div class="pen" aria-hidden="true"></div> Brainstorm on a Napkin — Biomedical Engineering</div>
    <div class="board" id="board">
      <!-- Hand-drawn filter -->
      <svg width="0" height="0">
        <filter id="squiggle">
          <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="1" seed="3" result="noise"/>
          <feDisplacementMap in="SourceGraphic" in2="noise" scale="1.3" xChannelSelector="R" yChannelSelector="G"/>
        </filter>
      </svg>

      <!-- Connectors -->
      <svg class="lines" viewBox="0 0 1400 900" preserveAspectRatio="none">
        <!-- Level 1 thick connectors -->
        <path class="stroke thick" d="M550,330 C670,300 740,280 820,240" />
        <path class="stroke thick" d="M550,330 C430,300 360,280 280,240" />
        <path class="stroke thick" d="M550,350 C540,430 520,500 520,560" />

        <!-- Imaging sub-branches -->
        <path class="stroke" d="M825,240 C900,250 940,260 980,240" />
        <path class="stroke" d="M825,240 C900,210 955,200 1000,180" />
        <path class="stroke" d="M825,240 C880,280 930,305 970,320" />

        <!-- Electronics sub-branches -->
        <path class="stroke" d="M280,240 C210,220 160,210 130,190" />
        <path class="stroke" d="M280,240 C210,260 170,280 140,310" />
        <path class="stroke" d="M280,240 C230,280 200,315 180,350" />

        <!-- Biomech sub-branches -->
        <path class="stroke" d="M520,560 C520,610 520,640 520,660" />
        <path class="stroke" d="M520,560 C580,600 620,620 660,640" />
        <path class="stroke" d="M520,560 C460,600 430,620 400,640" />
      </svg>

      <!-- Center -->
      <div class="node center" style="left:640px;top:380px;--rot:-1deg">
        <h3 class="ink">Biomedical Engineering</h3>
      </div>

      <!-- Level 1 Nodes -->
      <div class="node" style="left:810px;top:215px;--rot:1.5deg">
        <h3><span class="icon">🧠</span> Biomedical Imaging</h3>
        <div style="font-size:13px;opacity:.85">see inside, safely</div>
      </div>

      <div class="node" style="left:225px;top:210px;--rot:-2deg">
        <h3><span class="icon">❤️</span> Medical Electronics</h3>
        <div style="font-size:13px;opacity:.85">sense • measure • protect</div>
      </div>

      <div class="node" style="left:490px;top:560px;--rot:1deg">
        <h3><span class="icon">🏃</span> Biomechanics & Rehab</h3>
        <div style="font-size:13px;opacity:.85">movement • assist • restore</div>
      </div>

      <!-- Imaging Level 2 -->
      <div class="node" style="left:990px;top:222px;--rot:-2deg">
        <div class="hl">How We See Inside</div>
        <div class="leaf" style="--rot:-2deg">MRI</div>
        <div class="leaf" style="--rot:1deg">Ultrasound</div>
      </div>
      <div class="node" style="left:1005px;top:160px;--rot:1deg">
        <div>The Hardware</div>
        <div style="font-size:13px;opacity:.9">detectors • transducers</div>
      </div>
      <div class="node" style="left:965px;top:315px;--rot:-1deg">
        <div>Making the Picture Clear</div>
        <div style="font-size:13px;opacity:.9">reconstruction • denoise</div>
      </div>

      <!-- Electronics Level 2 -->
      <div class="node" style="left:75px;top:168px;--rot:-1deg">
        <div class="hl">Listening to the Body</div>
        <div class="leaf" style="--rot:.5deg">ECG</div>
        <div class="leaf" style="--rot:-.5deg">EEG</div>
      </div>
      <div class="node" style="left:80px;top:300px;--rot:1.5deg">
        <div>Boosting Signals</div>
        <div style="font-size:13px;opacity:.9">instrumentation amps</div>
      </div>
      <div class="node" style="left:120px;top:360px;--rot:-1deg">
        <div>Understanding the Data</div>
        <div style="font-size:13px;opacity:.9">filters • features</div>
      </div>

      <!-- Biomech Level 2 -->
      <div class="node" style="left:505px;top:662px;--rot:-1deg">
        <div>Analyzing Motion</div>
        <div style="font-size:13px;opacity:.9">gait • kinetics</div>
      </div>
      <div class="node" style="left:640px;top:642px;--rot:2deg">
        <div class="hl">Advanced Limbs</div>
        <div class="leaf" style="--rot:.6deg">Socket Design</div>
        <div class="leaf" style="--rot:-.6deg">Robotics</div>
      </div>
      <div class="node" style="left:375px;top:642px;--rot:-2deg">
        <div>Tech for Recovery</div>
        <div style="font-size:13px;opacity:.9">rehab tech • FES</div>
      </div>
    </div>

    <div class="legend">
      <span class="badge">Ink = main ideas</span>
      <span class="badge" style="background:linear-gradient(#fff 60%, var(--accent) 60% 100%)">Highlighter = examples</span>
      <span class="badge" style="background:#eaffff;border-color:#2c6e6b;color:#1b4443">Doodles = quick cues</span>
      <span style="float:right">Style: hand‑drawn • minimalist • napkin</span>
    </div>
  </div>
</body>
</html>
