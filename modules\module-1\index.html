<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Module 1 · Principles of Medical Imaging</title>
  <link rel="stylesheet" href="../../assets/index-d7x91drb.css">
  <style>
    .container{max-width:1000px;margin:0 auto;padding:24px}
    .card{background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:20px;margin:16px 0}
    .grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:16px}
    .btn{display:inline-block;padding:10px 14px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b}
    .btn:hover{border-color:#3f4580;background:#1c2040}
    header,footer{padding:16px 0}
    h1,h2,h3{color:#e6e9ff}
    p,li{color:#b9bfd6}
    .badge{font-size:.8rem;color:#9fb1ff;background:#1a2350;border:1px solid #3040a0;border-radius:999px;padding:4px 10px}
  </style>
  <script>
    // Simple content JSON powering dynamic rendering
    const lessons = [
      {
        id: "mri",
        title: "MRI Physics",
        summary: "Spin physics, T1/T2, k-space, sequences.",
        href: "./mri.html",
        bullets: ["Larmor frequency", "T1/T2/T2*", "k-space", "Spin/Gradient Echo"]
      },
      {
        id: "ct",
        title: "CT Reconstruction",
        summary: "Projection data, filtered backprojection, dose & artifacts.",
        href: "./ct.html",
        bullets: ["Radon transform", "FBP & ramp filter", "Iterative methods", "Dose vs. noise"]
      },
      {
        id: "ultrasound",
        title: "Ultrasound Signal Processing",
        summary: "Transducers, beamforming, envelope detection, Doppler.",
        href: "./ultrasound.html",
        bullets: ["Piezoelectric TX/RX", "DAS beamforming", "Envelope/log", "Doppler"]
      },
      {
        id: "contrast",
        title: "Image Contrast Agents",
        summary: "CT iodinated, MRI gadolinium, ultrasound microbubbles.",
        href: "./contrast.html",
        bullets: ["CT iodine", "MRI Gd chelates", "US microbubbles", "Safety"]
      }
    ];

    function $(q, r=document){return r.querySelector(q)}
    function $$(q, r=document){return Array.from(r.querySelectorAll(q))}
    function renderCards(filter=""){
      const wrap = $("#lesson-grid");
      wrap.innerHTML = "";
      const q = filter.trim().toLowerCase();
      lessons
        .filter(l => !q || l.title.toLowerCase().includes(q) || l.summary.toLowerCase().includes(q) || l.bullets.join(" ").toLowerCase().includes(q))
        .forEach(l => {
          const card = document.createElement("a");
          card.className = "card btn";
          card.href = l.href;
          card.innerHTML = `
            <h3>${l.title}</h3>
            <p>${l.summary}</p>
            <ul style="margin:6px 0 0 18px; color:#9fb1ff; font-size:.9rem;">
              ${l.bullets.map(b=>`<li>${b}</li>`).join("")}
            </ul>
          `;
          wrap.appendChild(card);
        });
      $("#count").textContent = wrap.children.length;
    }

    function exportModuleJSON(){
      const data = { module: "Principles of Medical Imaging", lessons };
      const blob = new Blob([JSON.stringify(data, null, 2)], {type:"application/json"});
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url; a.download = "module-1.json"; a.click();
      URL.revokeObjectURL(url);
    }

    document.addEventListener("DOMContentLoaded", () => {
      renderCards();
      $("#search").addEventListener("input", (e)=> renderCards(e.target.value));
      $("#export").addEventListener("click", exportModuleJSON);
      $("#present").addEventListener("click", () => {
        // Navigate to presentation view with hash parameter
        window.location.href = "./present.html#module-1";
      });
    });
  </script>
</head>
<body>
  <div class="container">
    <header>
      <a class="btn" href="../../index.html">← Back to Catalog</a>
      <h1>Module 1: Principles of Medical Imaging</h1>
      <p class="badge">Biomedical Imaging & Instrumentation Technology</p>
      <p>Start here to learn the fundamentals behind MRI, CT, Ultrasound, and contrast mechanisms before diving into detectors and reconstruction.</p>
    </header>

    <section class="card">
      <h2>Learning Objectives</h2>
      <ul>
        <li>Explain core physics behind MRI, CT, and Ultrasound.</li>
        <li>Describe sampling, reconstruction basics, and image quality metrics.</li>
        <li>Identify how contrast agents alter image contrast and safety considerations.</li>
      </ul>
    </section>

    <section class="card">
      <h2>Lessons <span style="font-size:.9rem;color:#9fb1ff">( <span id="count">0</span> )</span></h2>
      <div style="display:flex;gap:8px;flex-wrap:wrap;margin-bottom:12px">
        <input id="search" aria-label="Search lessons" placeholder="Search lessons or topics..." style="flex:1;min-width:220px;background:#0b0f1f;border:1px solid #1f2336;border-radius:10px;padding:10px;color:#e6e9ff"/>
        <button id="present" class="btn">Start Presentation</button>
        <button id="export" class="btn">Export JSON</button>
      </div>
      <div id="lesson-grid" class="grid"></div>
    </section>

    <footer>
      <p>End of Module 1. Proceed through lessons in order; each lesson contains objectives, key concepts, equations, visuals, and a brief quiz.</p>
    </footer>
  </div>
</body>
</html>
