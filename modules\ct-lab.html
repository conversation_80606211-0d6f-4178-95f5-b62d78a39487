<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CT Laboratory - Biomedical Imaging</title>
  <style>
    :root {
      --bg: #101828;
      --panel: #1a2332;
      --text: #f8fafc;
      --soft: #cbd5e1;
      --muted: #64748b;
      --accent: #2E90FA;
      --teal: #22d3ee;
      --ok: #10b981;
      --warn: #f59e0b;
      --error: #ef4444;
      --radius: 12px;
      --shadow: 0 20px 40px rgba(0,0,0,0.3);
      --glow: 0 0 20px rgba(46,144,250,0.3);
      --gap: 16px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: var(--bg);
      color: var(--text);
      line-height: 1.6;
      overflow-x: hidden;
    }

    /* Header */
    .header {
      background: linear-gradient(135deg, var(--panel), rgba(245,158,11,0.1));
      border-bottom: 1px solid rgba(255,255,255,0.1);
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .ct-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--warn), #ff6b35);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 0 20px rgba(245,158,11,0.3);
    }

    .title-text h1 {
      font-size: 24px;
      font-weight: 800;
      margin-bottom: 4px;
    }

    .title-text p {
      font-size: 14px;
      color: var(--muted);
    }

    .nav-actions {
      display: flex;
      gap: 12px;
    }

    .nav-btn {
      background: rgba(255,255,255,0.05);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 8px;
      color: var(--soft);
      padding: 10px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .nav-btn:hover {
      background: rgba(245,158,11,0.1);
      border-color: var(--warn);
      color: var(--warn);
    }

    .nav-btn.primary {
      background: linear-gradient(135deg, var(--warn), #ff6b35);
      border-color: var(--warn);
      color: white;
    }

    /* Main Container */
    .container {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      gap: 24px;
      padding: 24px;
      height: calc(100vh - 88px);
    }

    /* Panel Styles */
    .panel {
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
      border: 1px solid rgba(255,255,255,0.08);
      border-radius: 16px;
      box-shadow: var(--shadow);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .panel-header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid rgba(255,255,255,0.08);
      background: linear-gradient(180deg, rgba(245,158,11,0.08), transparent);
    }

    .panel-title {
      font-weight: 800;
      font-size: 16px;
      color: var(--text);
      margin-bottom: 4px;
    }

    .panel-subtitle {
      font-size: 12px;
      color: var(--muted);
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
    }

    /* CT Scanner Visualization */
    .ct-scanner {
      width: 100%;
      height: 400px;
      background: radial-gradient(ellipse at center, #1e293b 0%, #0f172a 100%);
      border-radius: 12px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(245,158,11,0.3);
    }

    .scanner-gantry {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 250px;
      height: 250px;
      border-radius: 50%;
      background: linear-gradient(45deg, #374151, #4b5563);
      border: 4px solid var(--warn);
      box-shadow: inset 0 0 30px rgba(245,158,11,0.3);
    }

    .scanner-bore {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 180px;
      height: 180px;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(245,158,11,0.1), transparent);
      border: 2px solid rgba(245,158,11,0.5);
    }

    .xray-tube {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 20px;
      background: var(--warn);
      border-radius: 4px;
      box-shadow: 0 0 15px var(--warn);
      animation: rotate 3s linear infinite;
    }

    .detector-array {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 15px;
      background: linear-gradient(90deg, var(--accent), var(--teal));
      border-radius: 2px;
      animation: rotate 3s linear infinite;
    }

    .patient-table {
      position: absolute;
      bottom: 15%;
      left: 50%;
      transform: translateX(-50%);
      width: 300px;
      height: 60px;
      background: linear-gradient(135deg, #374151, #4b5563);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    }

    .xray-beam {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      background: conic-gradient(from 0deg, transparent 0deg, rgba(245,158,11,0.1) 45deg, transparent 90deg);
      border-radius: 50%;
      animation: rotate 3s linear infinite;
      opacity: 0.7;
    }

    @keyframes rotate {
      from { transform: translate(-50%, -50%) rotate(0deg); }
      to { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Control Panels */
    .control-group {
      margin-bottom: 24px;
      background: rgba(255,255,255,0.02);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: 12px;
      padding: 20px;
    }

    .control-title {
      font-size: 14px;
      font-weight: 700;
      color: var(--warn);
      margin-bottom: 16px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .parameter-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .parameter-label {
      font-size: 12px;
      color: var(--soft);
      font-weight: 600;
    }

    .parameter-value {
      font-size: 14px;
      font-weight: 700;
      color: var(--warn);
      background: rgba(245,158,11,0.1);
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid rgba(245,158,11,0.2);
    }

    .slider-container {
      margin-top: 8px;
    }

    .slider {
      width: 100%;
      height: 6px;
      border-radius: 3px;
      background: rgba(255,255,255,0.1);
      appearance: none;
      outline: none;
      cursor: pointer;
    }

    .slider::-webkit-slider-thumb {
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--warn);
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(245,158,11,0.4);
    }

    /* Scan Button */
    .scan-button {
      width: 100%;
      padding: 16px;
      background: linear-gradient(135deg, var(--warn), #ff6b35);
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(245,158,11,0.3);
      margin-top: 24px;
    }

    .scan-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(245,158,11,0.4);
    }

    .scan-button.scanning {
      background: linear-gradient(135deg, var(--error), #dc2626);
      animation: scanPulse 2s ease-in-out infinite;
    }

    @keyframes scanPulse {
      0%, 100% { box-shadow: 0 4px 16px rgba(239,68,68,0.3); }
      50% { box-shadow: 0 8px 32px rgba(239,68,68,0.6); }
    }

    /* Image Display */
    .image-display {
      background: #000;
      border-radius: 12px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid rgba(245,158,11,0.2);
    }

    .ct-image {
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, #4a5568 10%, #2d3748 50%, #1a202c 90%);
      border-radius: 8px;
      position: relative;
      opacity: 0.9;
    }

    .bone-structure {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 180px;
      height: 160px;
      border: 3px solid rgba(245,158,11,0.8);
      border-radius: 50% 50% 40% 40%;
      background: linear-gradient(45deg, rgba(245,158,11,0.2), rgba(255,107,53,0.1));
    }

    .soft-tissue {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 220px;
      height: 200px;
      border: 2px solid rgba(46,144,250,0.4);
      border-radius: 50%;
      background: linear-gradient(45deg, rgba(46,144,250,0.1), transparent);
    }

    .image-overlay {
      position: absolute;
      top: 16px;
      left: 16px;
      background: rgba(0,0,0,0.8);
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      color: var(--soft);
    }

    .protocol-selector {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 20px;
    }

    .protocol-btn {
      background: rgba(245,158,11,0.1);
      border: 1px solid rgba(245,158,11,0.2);
      border-radius: 8px;
      color: var(--warn);
      padding: 12px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
    }

    .protocol-btn:hover {
      background: rgba(245,158,11,0.2);
      border-color: var(--warn);
    }

    .protocol-btn.active {
      background: var(--warn);
      color: white;
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="header-title">
      <div class="ct-icon">⚡</div>
      <div class="title-text">
        <h1>CT Scanner Laboratory</h1>
        <p>Computed Tomography - X-ray Imaging Simulation</p>
      </div>
    </div>
    <div class="nav-actions">
      <a href="biomedical-imaging-instrumentation-splash.html" class="nav-btn">
        ← Back to Main Lab
      </a>
      <button class="nav-btn primary" onclick="startHelicalScan()">
        Start Helical Scan
      </button>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container">
    <!-- Left Panel: CT Parameters & Protocols -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">CT Parameters & Protocols</div>
        <div class="panel-subtitle">X-ray tube and detector settings</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Scan Protocols</div>
          <div class="protocol-selector">
            <button class="protocol-btn active" onclick="loadProtocol('head')">Head</button>
            <button class="protocol-btn" onclick="loadProtocol('chest')">Chest</button>
            <button class="protocol-btn" onclick="loadProtocol('abdomen')">Abdomen</button>
            <button class="protocol-btn" onclick="loadProtocol('cardiac')">Cardiac</button>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">X-ray Tube</div>
          <div class="parameter-item">
            <span class="parameter-label">kVp (Tube Voltage)</span>
            <span class="parameter-value" id="kvp-value">120kVp</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="80" max="140" step="10" value="120" 
                   onchange="updateCTParameter('kvp', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">mAs (Tube Current)</span>
            <span class="parameter-value" id="mas-value">200mAs</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="50" max="500" step="25" value="200" 
                   onchange="updateCTParameter('mas', this.value)">
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Acquisition</div>
          <div class="parameter-item">
            <span class="parameter-label">Slice Thickness</span>
            <span class="parameter-value" id="slice-value">5mm</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="0.5" max="10" step="0.5" value="5" 
                   onchange="updateCTParameter('slice', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">Pitch</span>
            <span class="parameter-value" id="pitch-value">1.0</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="0.5" max="2.0" step="0.1" value="1.0" 
                   onchange="updateCTParameter('pitch', this.value)">
          </div>
        </div>

        <button class="scan-button" id="ct-scan-btn" onclick="toggleCTScan()">
          Start CT Scan
        </button>
      </div>
    </div>

    <!-- Center Panel: CT Scanner Visualization -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">CT Scanner</div>
        <div class="panel-subtitle">Real-time X-ray acquisition</div>
      </div>
      <div class="panel-content">
        <div class="ct-scanner">
          <div class="scanner-gantry">
            <div class="scanner-bore"></div>
            <div class="xray-tube"></div>
            <div class="detector-array"></div>
            <div class="xray-beam"></div>
          </div>
          <div class="patient-table"></div>
        </div>
        
        <div class="image-display" style="margin-top: 24px; height: 350px;">
          <div class="image-overlay">
            Axial CT | 120kVp | 5mm slice | W:400 L:40
          </div>
          <div class="ct-image">
            <div class="soft-tissue"></div>
            <div class="bone-structure"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel: Image Analysis & Dose -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">Image Analysis & Dose</div>
        <div class="panel-subtitle">Quality metrics and radiation dose</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Image Quality</div>
          <div class="parameter-item">
            <span class="parameter-label">Noise Level</span>
            <span class="parameter-value" id="noise-display">12 HU</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Contrast Resolution</span>
            <span class="parameter-value" id="contrast-display">5 HU</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Spatial Resolution</span>
            <span class="parameter-value" id="resolution-display">0.5mm</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Radiation Dose</div>
          <div class="parameter-item">
            <span class="parameter-label">CTDIvol</span>
            <span class="parameter-value" id="ctdi-display">15.2 mGy</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">DLP</span>
            <span class="parameter-value" id="dlp-display">456 mGy·cm</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Effective Dose</span>
            <span class="parameter-value" id="dose-display">2.1 mSv</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Scan Progress</div>
          <div class="parameter-item">
            <span class="parameter-label">Scan Time</span>
            <span class="parameter-value" id="scan-time">00:00</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Slices Acquired</span>
            <span class="parameter-value" id="slice-progress">0/120</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let isScanning = false;
    let scanProgress = 0;
    let scanTimer = null;

    const protocols = {
      head: { kvp: 120, mas: 300, slice: 5, pitch: 1.0 },
      chest: { kvp: 120, mas: 150, slice: 1.25, pitch: 1.5 },
      abdomen: { kvp: 120, mas: 200, slice: 2.5, pitch: 1.2 },
      cardiac: { kvp: 100, mas: 400, slice: 0.6, pitch: 0.2 }
    };

    function loadProtocol(protocolName) {
      // Update active button
      document.querySelectorAll('.protocol-btn').forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');
      
      const protocol = protocols[protocolName];
      
      // Update sliders and displays
      document.querySelector('input[onchange*="kvp"]').value = protocol.kvp;
      document.querySelector('input[onchange*="mas"]').value = protocol.mas;
      document.querySelector('input[onchange*="slice"]').value = protocol.slice;
      document.querySelector('input[onchange*="pitch"]').value = protocol.pitch;
      
      updateCTParameter('kvp', protocol.kvp);
      updateCTParameter('mas', protocol.mas);
      updateCTParameter('slice', protocol.slice);
      updateCTParameter('pitch', protocol.pitch);
    }

    function updateCTParameter(parameter, value) {
      const displays = {
        kvp: 'kvp-value',
        mas: 'mas-value',
        slice: 'slice-value',
        pitch: 'pitch-value'
      };
      
      const units = {
        kvp: 'kVp',
        mas: 'mAs',
        slice: 'mm',
        pitch: ''
      };
      
      document.getElementById(displays[parameter]).textContent = value + units[parameter];
      
      // Update dose calculations
      updateDoseCalculations();
      updateImageQuality(parameter, value);
    }

    function updateDoseCalculations() {
      const kvp = parseInt(document.getElementById('kvp-value').textContent);
      const mas = parseInt(document.getElementById('mas-value').textContent);
      
      // Simplified dose calculations
      const ctdi = (kvp * mas / 1000 * 0.15).toFixed(1);
      const dlp = (ctdi * 30).toFixed(0);
      const effectiveDose = (dlp * 0.0046).toFixed(1);
      
      document.getElementById('ctdi-display').textContent = ctdi + ' mGy';
      document.getElementById('dlp-display').textContent = dlp + ' mGy·cm';
      document.getElementById('dose-display').textContent = effectiveDose + ' mSv';
    }

    function updateImageQuality(parameter, value) {
      if (parameter === 'mas') {
        const noise = Math.max(5, 20 - (value / 50));
        document.getElementById('noise-display').textContent = noise.toFixed(0) + ' HU';
      }
      
      if (parameter === 'slice') {
        document.getElementById('resolution-display').textContent = (value / 10).toFixed(1) + 'mm';
      }
    }

    function toggleCTScan() {
      const button = document.getElementById('ct-scan-btn');
      
      if (!isScanning) {
        startCTScan();
        button.textContent = 'Stop Scan';
        button.classList.add('scanning');
      } else {
        stopCTScan();
        button.textContent = 'Start CT Scan';
        button.classList.remove('scanning');
      }
    }

    function startCTScan() {
      isScanning = true;
      scanProgress = 0;
      let seconds = 0;
      
      scanTimer = setInterval(() => {
        seconds++;
        scanProgress = Math.min(100, (seconds / 20) * 100); // 20 second scan
        
        // Update displays
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        document.getElementById('scan-time').textContent = 
          `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        
        document.getElementById('slice-progress').textContent = 
          `${Math.floor(scanProgress / 100 * 120)}/120`;
        
        if (scanProgress >= 100) {
          completeCTScan();
        }
      }, 1000);
    }

    function stopCTScan() {
      isScanning = false;
      if (scanTimer) {
        clearInterval(scanTimer);
        scanTimer = null;
      }
      document.getElementById('scan-time').textContent = '00:00';
      document.getElementById('slice-progress').textContent = '0/120';
    }

    function completeCTScan() {
      stopCTScan();
      document.getElementById('ct-scan-btn').textContent = 'Start CT Scan';
      document.getElementById('ct-scan-btn').classList.remove('scanning');
      
      alert('CT Scan completed successfully!');
    }

    function startHelicalScan() {
      alert('Helical CT protocols will be available in the next update!');
    }

    // Initialize with head protocol
    document.addEventListener('DOMContentLoaded', function() {
      loadProtocol('head');
    });
  </script>
</body>
</html>
