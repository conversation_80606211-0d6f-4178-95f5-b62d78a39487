<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Ultrasound Laboratory - Biomedical Imaging</title>
  <style>
    :root {
      --bg: #101828;
      --panel: #1a2332;
      --text: #f8fafc;
      --soft: #cbd5e1;
      --muted: #64748b;
      --accent: #2E90FA;
      --teal: #22d3ee;
      --ok: #10b981;
      --warn: #f59e0b;
      --error: #ef4444;
      --radius: 12px;
      --shadow: 0 20px 40px rgba(0,0,0,0.3);
      --glow: 0 0 20px rgba(46,144,250,0.3);
      --gap: 16px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: var(--bg);
      color: var(--text);
      line-height: 1.6;
      overflow-x: hidden;
    }

    /* Header */
    .header {
      background: linear-gradient(135deg, var(--panel), rgba(16,185,129,0.1));
      border-bottom: 1px solid rgba(255,255,255,0.1);
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .us-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--ok), var(--teal));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 0 20px rgba(16,185,129,0.3);
    }

    .title-text h1 {
      font-size: 24px;
      font-weight: 800;
      margin-bottom: 4px;
    }

    .title-text p {
      font-size: 14px;
      color: var(--muted);
    }

    .nav-actions {
      display: flex;
      gap: 12px;
    }

    .nav-btn {
      background: rgba(255,255,255,0.05);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 8px;
      color: var(--soft);
      padding: 10px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .nav-btn:hover {
      background: rgba(16,185,129,0.1);
      border-color: var(--ok);
      color: var(--ok);
    }

    .nav-btn.primary {
      background: linear-gradient(135deg, var(--ok), var(--teal));
      border-color: var(--ok);
      color: white;
    }

    /* Main Container */
    .container {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      gap: 24px;
      padding: 24px;
      height: calc(100vh - 88px);
    }

    /* Panel Styles */
    .panel {
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
      border: 1px solid rgba(255,255,255,0.08);
      border-radius: 16px;
      box-shadow: var(--shadow);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .panel-header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid rgba(255,255,255,0.08);
      background: linear-gradient(180deg, rgba(16,185,129,0.08), transparent);
    }

    .panel-title {
      font-weight: 800;
      font-size: 16px;
      color: var(--text);
      margin-bottom: 4px;
    }

    .panel-subtitle {
      font-size: 12px;
      color: var(--muted);
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
    }

    /* Ultrasound System Visualization */
    .us-system {
      width: 100%;
      height: 400px;
      background: radial-gradient(ellipse at center, #1e293b 0%, #0f172a 100%);
      border-radius: 12px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(16,185,129,0.3);
    }

    .us-probe {
      position: absolute;
      top: 20%;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 120px;
      background: linear-gradient(180deg, #4b5563, #374151);
      border-radius: 30px 30px 8px 8px;
      border: 2px solid var(--ok);
      box-shadow: 0 0 20px rgba(16,185,129,0.3);
    }

    .us-cable {
      position: absolute;
      top: 10%;
      left: 50%;
      transform: translateX(-50%);
      width: 8px;
      height: 80px;
      background: #374151;
      border-radius: 4px;
    }

    .sound-waves {
      position: absolute;
      top: 35%;
      left: 50%;
      transform: translateX(-50%);
      width: 200px;
      height: 200px;
    }

    .wave {
      position: absolute;
      border: 2px solid rgba(16,185,129,0.4);
      border-radius: 50%;
      animation: waveExpand 2s ease-out infinite;
    }

    .wave:nth-child(1) { animation-delay: 0s; }
    .wave:nth-child(2) { animation-delay: 0.5s; }
    .wave:nth-child(3) { animation-delay: 1s; }
    .wave:nth-child(4) { animation-delay: 1.5s; }

    @keyframes waveExpand {
      0% {
        width: 20px;
        height: 20px;
        top: 50%;
        left: 50%;
        opacity: 1;
      }
      100% {
        width: 200px;
        height: 200px;
        top: 0;
        left: 0;
        opacity: 0;
      }
    }

    .tissue-phantom {
      position: absolute;
      bottom: 20%;
      left: 50%;
      transform: translateX(-50%);
      width: 250px;
      height: 100px;
      background: linear-gradient(135deg, rgba(16,185,129,0.2), rgba(34,211,238,0.1));
      border-radius: 12px;
      border: 1px solid rgba(16,185,129,0.3);
    }

    /* Control Panels */
    .control-group {
      margin-bottom: 24px;
      background: rgba(255,255,255,0.02);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: 12px;
      padding: 20px;
    }

    .control-title {
      font-size: 14px;
      font-weight: 700;
      color: var(--ok);
      margin-bottom: 16px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .parameter-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .parameter-label {
      font-size: 12px;
      color: var(--soft);
      font-weight: 600;
    }

    .parameter-value {
      font-size: 14px;
      font-weight: 700;
      color: var(--ok);
      background: rgba(16,185,129,0.1);
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid rgba(16,185,129,0.2);
    }

    .slider-container {
      margin-top: 8px;
    }

    .slider {
      width: 100%;
      height: 6px;
      border-radius: 3px;
      background: rgba(255,255,255,0.1);
      appearance: none;
      outline: none;
      cursor: pointer;
    }

    .slider::-webkit-slider-thumb {
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--ok);
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(16,185,129,0.4);
    }

    /* Mode Selector */
    .mode-selector {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 20px;
    }

    .mode-btn {
      background: rgba(16,185,129,0.1);
      border: 1px solid rgba(16,185,129,0.2);
      border-radius: 8px;
      color: var(--ok);
      padding: 10px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      font-weight: 600;
    }

    .mode-btn:hover {
      background: rgba(16,185,129,0.2);
      border-color: var(--ok);
    }

    .mode-btn.active {
      background: var(--ok);
      color: white;
    }

    /* Scan Button */
    .scan-button {
      width: 100%;
      padding: 16px;
      background: linear-gradient(135deg, var(--ok), var(--teal));
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(16,185,129,0.3);
      margin-top: 24px;
    }

    .scan-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(16,185,129,0.4);
    }

    .scan-button.scanning {
      background: linear-gradient(135deg, var(--accent), var(--teal));
      animation: scanPulse 2s ease-in-out infinite;
    }

    @keyframes scanPulse {
      0%, 100% { box-shadow: 0 4px 16px rgba(46,144,250,0.3); }
      50% { box-shadow: 0 8px 32px rgba(46,144,250,0.6); }
    }

    /* Image Display */
    .image-display {
      background: #000;
      border-radius: 12px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid rgba(16,185,129,0.2);
    }

    .us-image {
      width: 300px;
      height: 300px;
      background: radial-gradient(ellipse 80% 60% at 50% 20%, #1a202c 0%, #000 100%);
      border-radius: 8px;
      position: relative;
      opacity: 0.9;
    }

    .echo-pattern {
      position: absolute;
      top: 30%;
      left: 50%;
      transform: translateX(-50%);
      width: 200px;
      height: 150px;
      background: 
        repeating-linear-gradient(
          0deg,
          transparent 0px,
          rgba(16,185,129,0.3) 2px,
          transparent 4px,
          transparent 8px
        );
      border-radius: 50% 50% 40% 40%;
    }

    .organ-structure {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120px;
      height: 80px;
      border: 2px solid rgba(16,185,129,0.6);
      border-radius: 50%;
      background: linear-gradient(45deg, rgba(16,185,129,0.2), transparent);
    }

    .image-overlay {
      position: absolute;
      top: 16px;
      left: 16px;
      background: rgba(0,0,0,0.8);
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      color: var(--soft);
    }

    .depth-scale {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      gap: 20px;
      font-size: 10px;
      color: var(--muted);
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="header-title">
      <div class="us-icon">🔊</div>
      <div class="title-text">
        <h1>Ultrasound Laboratory</h1>
        <p>Diagnostic Ultrasound - Real-time Imaging Simulation</p>
      </div>
    </div>
    <div class="nav-actions">
      <a href="biomedical-imaging-instrumentation-splash.html" class="nav-btn">
        ← Back to Main Lab
      </a>
      <button class="nav-btn primary" onclick="startDopplerMode()">
        Start Doppler
      </button>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container">
    <!-- Left Panel: Ultrasound Parameters -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">Ultrasound Parameters</div>
        <div class="panel-subtitle">Transducer and beam control</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Imaging Mode</div>
          <div class="mode-selector">
            <button class="mode-btn active" onclick="setMode('bmode')">B-Mode</button>
            <button class="mode-btn" onclick="setMode('doppler')">Doppler</button>
            <button class="mode-btn" onclick="setMode('color')">Color</button>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Transducer</div>
          <div class="parameter-item">
            <span class="parameter-label">Frequency</span>
            <span class="parameter-value" id="freq-value">5MHz</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="2" max="15" step="1" value="5" 
                   onchange="updateUSParameter('frequency', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">Power</span>
            <span class="parameter-value" id="power-value">50%</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="10" max="100" step="5" value="50" 
                   onchange="updateUSParameter('power', this.value)">
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Image Controls</div>
          <div class="parameter-item">
            <span class="parameter-label">Gain</span>
            <span class="parameter-value" id="gain-value">30dB</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="0" max="100" step="5" value="30" 
                   onchange="updateUSParameter('gain', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">Depth</span>
            <span class="parameter-value" id="depth-value">12cm</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="5" max="25" step="1" value="12" 
                   onchange="updateUSParameter('depth', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">Focus</span>
            <span class="parameter-value" id="focus-value">6cm</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="2" max="15" step="1" value="6" 
                   onchange="updateUSParameter('focus', this.value)">
          </div>
        </div>

        <button class="scan-button" id="us-scan-btn" onclick="toggleUSScan()">
          Start Ultrasound
        </button>
      </div>
    </div>

    <!-- Center Panel: Ultrasound System -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">Ultrasound System</div>
        <div class="panel-subtitle">Real-time acoustic imaging</div>
      </div>
      <div class="panel-content">
        <div class="us-system">
          <div class="us-cable"></div>
          <div class="us-probe"></div>
          <div class="sound-waves">
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
          </div>
          <div class="tissue-phantom"></div>
        </div>
        
        <div class="image-display" style="margin-top: 24px; height: 350px;">
          <div class="image-overlay">
            B-Mode | 5MHz | Depth: 12cm | Gain: 30dB
          </div>
          <div class="depth-scale">
            <div>2cm</div>
            <div>4cm</div>
            <div>6cm</div>
            <div>8cm</div>
            <div>10cm</div>
            <div>12cm</div>
          </div>
          <div class="us-image">
            <div class="echo-pattern"></div>
            <div class="organ-structure"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel: Measurements & Analysis -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">Measurements & Analysis</div>
        <div class="panel-subtitle">Image quality and measurements</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Image Quality</div>
          <div class="parameter-item">
            <span class="parameter-label">Penetration</span>
            <span class="parameter-value" id="penetration-display">Good</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Resolution</span>
            <span class="parameter-value" id="resolution-display">0.8mm</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Contrast</span>
            <span class="parameter-value" id="contrast-display">High</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Safety Parameters</div>
          <div class="parameter-item">
            <span class="parameter-label">Thermal Index</span>
            <span class="parameter-value" id="ti-display">0.3</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Mechanical Index</span>
            <span class="parameter-value" id="mi-display">0.7</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">ISPTA</span>
            <span class="parameter-value" id="ispta-display">45 mW/cm²</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Measurements</div>
          <div class="parameter-item">
            <span class="parameter-label">Distance 1</span>
            <span class="parameter-value" id="dist1-display">--</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Distance 2</span>
            <span class="parameter-value" id="dist2-display">--</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Area</span>
            <span class="parameter-value" id="area-display">--</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Scan Status</div>
          <div class="parameter-item">
            <span class="parameter-label">Frame Rate</span>
            <span class="parameter-value" id="frame-rate">30 fps</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Scan Time</span>
            <span class="parameter-value" id="scan-time">00:00</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let isScanning = false;
    let currentMode = 'bmode';
    let scanTimer = null;

    function setMode(mode) {
      currentMode = mode;
      
      // Update active button
      document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');
      
      // Update visualization based on mode
      updateModeVisualization(mode);
    }

    function updateModeVisualization(mode) {
      const waves = document.querySelectorAll('.wave');
      const echoPattern = document.querySelector('.echo-pattern');
      
      switch(mode) {
        case 'bmode':
          waves.forEach(wave => wave.style.borderColor = 'rgba(16,185,129,0.4)');
          echoPattern.style.background = `repeating-linear-gradient(
            0deg,
            transparent 0px,
            rgba(16,185,129,0.3) 2px,
            transparent 4px,
            transparent 8px
          )`;
          break;
        case 'doppler':
          waves.forEach(wave => wave.style.borderColor = 'rgba(239,68,68,0.6)');
          echoPattern.style.background = `repeating-linear-gradient(
            0deg,
            transparent 0px,
            rgba(239,68,68,0.4) 2px,
            transparent 4px,
            transparent 8px
          )`;
          break;
        case 'color':
          waves.forEach(wave => wave.style.borderColor = 'rgba(34,211,238,0.6)');
          echoPattern.style.background = `repeating-linear-gradient(
            0deg,
            transparent 0px,
            rgba(34,211,238,0.4) 2px,
            transparent 4px,
            transparent 8px
          )`;
          break;
      }
    }

    function updateUSParameter(parameter, value) {
      const displays = {
        frequency: 'freq-value',
        power: 'power-value',
        gain: 'gain-value',
        depth: 'depth-value',
        focus: 'focus-value'
      };
      
      const units = {
        frequency: 'MHz',
        power: '%',
        gain: 'dB',
        depth: 'cm',
        focus: 'cm'
      };
      
      document.getElementById(displays[parameter]).textContent = value + units[parameter];
      
      // Update safety parameters
      updateSafetyParameters();
      updateImageQuality(parameter, value);
    }

    function updateSafetyParameters() {
      const power = parseInt(document.getElementById('power-value').textContent);
      const frequency = parseInt(document.getElementById('freq-value').textContent);
      
      // Simplified safety calculations
      const ti = (power * frequency / 1000).toFixed(1);
      const mi = (Math.sqrt(power) / 10).toFixed(1);
      const ispta = (power * 2).toFixed(0);
      
      document.getElementById('ti-display').textContent = ti;
      document.getElementById('mi-display').textContent = mi;
      document.getElementById('ispta-display').textContent = ispta + ' mW/cm²';
    }

    function updateImageQuality(parameter, value) {
      if (parameter === 'frequency') {
        const resolution = (2 / value).toFixed(1);
        document.getElementById('resolution-display').textContent = resolution + 'mm';
        
        const penetration = value < 5 ? 'Excellent' : value < 8 ? 'Good' : 'Limited';
        document.getElementById('penetration-display').textContent = penetration;
      }
      
      if (parameter === 'gain') {
        const contrast = value < 20 ? 'Low' : value < 60 ? 'Medium' : 'High';
        document.getElementById('contrast-display').textContent = contrast;
      }
    }

    function toggleUSScan() {
      const button = document.getElementById('us-scan-btn');
      
      if (!isScanning) {
        startUSScan();
        button.textContent = 'Stop Ultrasound';
        button.classList.add('scanning');
      } else {
        stopUSScan();
        button.textContent = 'Start Ultrasound';
        button.classList.remove('scanning');
      }
    }

    function startUSScan() {
      isScanning = true;
      let seconds = 0;
      
      scanTimer = setInterval(() => {
        seconds++;
        
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        document.getElementById('scan-time').textContent = 
          `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        
        // Simulate frame rate variations
        const frameRate = 25 + Math.floor(Math.random() * 10);
        document.getElementById('frame-rate').textContent = frameRate + ' fps';
        
      }, 1000);
    }

    function stopUSScan() {
      isScanning = false;
      if (scanTimer) {
        clearInterval(scanTimer);
        scanTimer = null;
      }
      document.getElementById('scan-time').textContent = '00:00';
      document.getElementById('frame-rate').textContent = '30 fps';
    }

    function startDopplerMode() {
      setMode('doppler');
      document.querySelectorAll('.mode-btn')[1].click();
      alert('Doppler mode activated! Adjust frequency for optimal flow detection.');
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      updateSafetyParameters();
      updateImageQuality('frequency', 5);
    });
  </script>
</body>
</html>
