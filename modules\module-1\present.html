<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Module 1 Presentation</title>
  <link rel="stylesheet" href="../../assets/index-d7x91drb.css">
  <style>
    :root{--bg:#0b0f1f;--card:#0f1220;--muted:#a9b6d3;--accent:#5EEAD4;--pri:#2E90FA}
    html,body{height:100%}
    body{margin:0;background:radial-gradient(1200px 600px at 20% 0%, #0c1424 0%, var(--bg) 45%);color:#e6e9ff;font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif}
    .wrap{max-width:1100px;margin:0 auto;padding:18px}
    .bar{display:flex;align-items:center;justify-content:space-between;gap:10px}
    .bar .btn{padding:8px 12px;border-radius:10px;border:1px solid #293055;background:#131735;color:#e6e9ff;text-decoration:none}
    .bar .btn.primary{background:var(--pri);color:#061018;border-color:#2c72ff}
    .deck{margin-top:14px;background:linear-gradient(180deg,#0f1328,#0b0f20);border:1px solid #1f2336;border-radius:14px;min-height:480px;display:grid;grid-template-columns:280px 1fr}
    .sidebar{border-right:1px solid #1f2336;padding:12px;overflow:auto}
    .search{width:100%;background:#0b0f1f;border:1px solid #1f2336;border-radius:10px;padding:10px;color:#e6e9ff;margin-bottom:10px}
    .topic{display:block;background:#11172f;border:1px solid #1f2336;border-radius:10px;padding:10px;margin:8px 0;color:#cfe1ff}
    .topic.active{border-color:#2E90FA;background:#112044}
    .stage{padding:16px}
    .slide{display:none}
    .slide.active{display:block;animation:fade .25s ease}
    .card{background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:18px;margin:12px 0}
    .muted{color:#a9b6d3}
    .kbd{background:#151933;border:1px solid #252a4a;border-radius:6px;padding:2px 6px}
    .controls{display:flex;align-items:center;justify-content:space-between;gap:10px;margin-top:10px}
    .controls .btn{padding:10px 14px;border-radius:10px;border:1px solid #283050;background:#141a33;color:#e6e9ff}
    .controls .btn:hover{background:#1a2144}
    .prog{height:6px;border-radius:999px;background:#12162a;border:1px solid #1f2336;overflow:hidden}
    .prog > div{height:100%;background:linear-gradient(90deg,#2E90FA,#5EEAD4)}
    @keyframes fade{from{opacity:.6;transform:translateY(4px)}to{opacity:1;transform:none}}
    @media (max-width: 880px){.deck{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="bar">
      <div style="display:flex;gap:8px;align-items:center">
        <a class="btn" href="./index.html">← Module 1 Index</a>
        <strong>Interactive Presentation</strong>
        <span class="muted" id="lessonLabel"></span>
      </div>
      <div style="display:flex;gap:6px;align-items:center">
        <button class="btn" id="btnShuffle" title="Shuffle slides">Shuffle</button>
        <button class="btn primary" id="btnStart">Start</button>
      </div>
    </div>

    <div class="deck">
      <aside class="sidebar">
        <input id="filter" class="search" placeholder="Filter topics..."/>
        <nav id="toc"></nav>
      </aside>

      <main class="stage">
        <div id="slides"></div>

        <div class="controls">
          <div class="prog" style="flex:1"><div id="bar" style="width:0%"></div></div>
          <div style="display:flex;gap:6px">
            <button class="btn" id="prev">← Prev (←)</button>
            <button class="btn" id="next">Next (→) →</button>
          </div>
        </div>

        <p class="muted" style="margin:8px 0 0">Tips: Use keyboard arrows, <span class="kbd">F</span> to toggle fullscreen, <span class="kbd">S</span> to shuffle. Print to PDF via browser print.</p>
      </main>
    </div>
  </div>

  <script>
    // Data model: slides per lesson; easily extended/edited
    const content = {
      "mri": {
        title: "MRI Physics",
        slides: [
          { h: "Overview", b: ["Spins precess at ω0=γB0","Contrast via T1/T2/T2*","Gradients encode in k-space"] },
          { h: "Relaxation", b: ["Mz(t)=M0(1−e^{−t/T1})","Mxy(t)=M0 e^{−t/T2*}","T2* ≤ T2 (inhomogeneity)"] },
          { h: "Spatial Encoding", b: ["k(t)=(γ/2π)∫G(t)dt","Frequency/phase encoding with Gx,Gy,Gz"] },
          { h: "Sequences", b: ["Spin‑Echo 90°‑τ‑180°‑τ","GRE, EPI tradeoffs"] },
          { h: "Artifacts", b: ["Aliasing (FOV)","Susceptibility","Motion"] }
        ]
      },
      "ct": {
        title: "CT Reconstruction",
        slides: [
          { h: "Projections", b: ["I=I0 e^{−∫μdl}","Log transform p=-ln(I/I0)"] },
          { h: "Fourier Slice", b: ["1D FT(projection) = central slice of 2D FT"] },
          { h: "FBP", b: ["FFT → |f| ramp × window → IFFT","Backproject and sum"] },
          { h: "Iterative", b: ["ART/SIRT","Penalized likelihood (TV)"] },
          { h: "Artifacts", b: ["Beam hardening","Metal streaks","Motion"] }
        ]
      },
      "ultrasound": {
        title: "Ultrasound Signal Processing",
        slides: [
          { h: "Transducers", b: ["f0≈c/(2t)","Bandwidth via backing/matching"] },
          { h: "Beamforming", b: ["DAS: y(t)=Σ w_i x_i(t+τ_i)","Apodization, dynamic focusing"] },
          { h: "Detection", b: ["IQ demodulation","Envelope, TGC, log comp."] },
          { h: "Doppler", b: ["f_d=2 v f0 cosθ / c","PW/Color flow"] }
        ]
      },
      "contrast": {
        title: "Image Contrast Agents",
        slides: [
          { h: "CT Iodinated", b: ["↑ photoelectric near K‑edge","Perfusion phases"] },
          { h: "MRI Gadolinium", b: ["Shortens T1 (↑ signal T1W)","NSF risk; macrocyclics"] },
          { h: "US Microbubbles", b: ["Nonlinear harmonics","Pulse inversion/AM"] },
          { h: "Safety", b: ["Renal screening","Allergy/AKI precautions"] }
        ]
      }
    };

    // Read lesson from hash (#mri, #ct, ...). Default: entire module concatenation.
    const hash = (location.hash || "#mri").replace("#","");
    const deckKeys = Object.keys(content);
    let currentLesson = deckKeys.includes(hash) ? hash : "mri";
    let slides = [];
    let i = 0;

    const $ = (q,r=document)=>r.querySelector(q);
    const $$ = (q,r=document)=>Array.from(r.querySelectorAll(q));

    function buildTOC(){
      const nav = $("#toc");
      nav.innerHTML = "";
      deckKeys.forEach(key=>{
        const a = document.createElement("a");
        a.href = `#${key}`;
        a.className = "topic" + (key===currentLesson?" active":"");
        a.textContent = content[key].title;
        a.addEventListener("click",(e)=>{
          e.preventDefault();
          selectLesson(key);
        });
        nav.appendChild(a);
      });
    }

    function selectLesson(key){
      currentLesson = key;
      location.hash = "#"+key;
      $$(".topic").forEach(t=>t.classList.toggle("active", t.textContent===content[key].title));
      $("#lessonLabel").textContent = "• "+content[key].title;
      slides = content[key].slides.map(s=>({...s}));
      i = 0;
      renderSlides();
    }

    function renderSlides(){
      const host = $("#slides");
      host.innerHTML = "";
      slides.forEach((s,idx)=>{
        const el = document.createElement("section");
        el.className = "slide card"+(idx===i?" active":"");
        el.innerHTML = `
          <h2 style="margin:0 0 8px">${s.h}</h2>
          <ul style="margin:8px 0 0 18px;color:#cfe1ff">
            ${s.b.map(x=>`<li>${x}</li>`).join("")}
          </ul>
        `;
        host.appendChild(el);
      });
      updateBar();
      scrollActiveIntoView();
    }

    function updateBar(){
      const pct = slides.length ? ((i+1)/slides.length)*100 : 0;
      $("#bar").style.width = pct.toFixed(1)+"%";
      $$(".slide").forEach((s,idx)=>s.classList.toggle("active",idx===i));
    }

    function next(){ if(i < slides.length-1){ i++; updateBar(); } }
    function prev(){ if(i > 0){ i--; updateBar(); } }

    function shuffle(){
      for(let j=slides.length-1;j>0;j--){
        const k = Math.floor(Math.random()*(j+1));
        [slides[j], slides[k]] = [slides[k], slides[j]];
      }
      i = 0; renderSlides();
    }

    function scrollActiveIntoView(){
      const active = $(".topic.active");
      if(active) active.scrollIntoView({block:"nearest"});
    }

    // Keyboard + controls
    document.addEventListener("keydown",(e)=>{
      if(e.key==="ArrowRight") next();
      else if(e.key==="ArrowLeft") prev();
      else if(e.key.toLowerCase()==="s") shuffle();
      else if(e.key.toLowerCase()==="f"){
        if(!document.fullscreenElement) document.documentElement.requestFullscreen().catch(()=>{});
        else document.exitFullscreen();
      }
    });
    $("#next").addEventListener("click", next);
    $("#prev").addEventListener("click", prev);
    $("#btnShuffle").addEventListener("click", shuffle);
    $("#btnStart").addEventListener("click", ()=>{ i=0; updateBar(); });

    // Filter topics
    $("#filter").addEventListener("input", (e)=>{
      const q = e.target.value.toLowerCase();
      $$("#toc .topic").forEach(a=>{
        a.style.display = a.textContent.toLowerCase().includes(q) ? "" : "none";
      });
    });

    // Init
    buildTOC();
    selectLesson(currentLesson);
  </script>
</body>
</html>
