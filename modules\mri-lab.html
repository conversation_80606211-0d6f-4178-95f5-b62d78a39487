<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MRI Laboratory - Biomedical Imaging</title>
  <style>
    :root {
      --bg: #101828;
      --panel: #1a2332;
      --text: #f8fafc;
      --soft: #cbd5e1;
      --muted: #64748b;
      --accent: #2E90FA;
      --teal: #22d3ee;
      --ok: #10b981;
      --warn: #f59e0b;
      --error: #ef4444;
      --radius: 12px;
      --shadow: 0 20px 40px rgba(0,0,0,0.3);
      --glow: 0 0 20px rgba(46,144,250,0.3);
      --gap: 16px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: var(--bg);
      color: var(--text);
      line-height: 1.6;
      overflow-x: hidden;
    }

    /* Header */
    .header {
      background: linear-gradient(135deg, var(--panel), rgba(46,144,250,0.1));
      border-bottom: 1px solid rgba(255,255,255,0.1);
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .mri-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--accent), var(--teal));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: var(--glow);
    }

    .title-text h1 {
      font-size: 24px;
      font-weight: 800;
      margin-bottom: 4px;
    }

    .title-text p {
      font-size: 14px;
      color: var(--muted);
    }

    .nav-actions {
      display: flex;
      gap: 12px;
    }

    .nav-btn {
      background: rgba(255,255,255,0.05);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 8px;
      color: var(--soft);
      padding: 10px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .nav-btn:hover {
      background: rgba(46,144,250,0.1);
      border-color: var(--accent);
      color: var(--accent);
    }

    .nav-btn.primary {
      background: linear-gradient(135deg, var(--accent), var(--teal));
      border-color: var(--accent);
      color: white;
    }

    /* Main Container */
    .container {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      gap: 24px;
      padding: 24px;
      height: calc(100vh - 88px);
    }

    /* Panel Styles */
    .panel {
      background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
      border: 1px solid rgba(255,255,255,0.08);
      border-radius: 16px;
      box-shadow: var(--shadow);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .panel-header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid rgba(255,255,255,0.08);
      background: linear-gradient(180deg, rgba(46,144,250,0.08), transparent);
    }

    .panel-title {
      font-weight: 800;
      font-size: 16px;
      color: var(--text);
      margin-bottom: 4px;
    }

    .panel-subtitle {
      font-size: 12px;
      color: var(--muted);
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
    }

    /* MRI Scanner Visualization */
    .mri-scanner {
      width: 100%;
      height: 400px;
      background: radial-gradient(ellipse at center, #1e293b 0%, #0f172a 100%);
      border-radius: 12px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(46,144,250,0.3);
    }

    .scanner-bore {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 200px;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(46,144,250,0.1), transparent);
      border: 3px solid var(--accent);
      animation: pulse 3s ease-in-out infinite;
    }

    .patient-table {
      position: absolute;
      bottom: 20%;
      left: 50%;
      transform: translateX(-50%);
      width: 300px;
      height: 60px;
      background: linear-gradient(135deg, #374151, #4b5563);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    }

    .magnetic-field {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        radial-gradient(ellipse 300px 100px at 50% 50%, rgba(34,211,238,0.1), transparent),
        radial-gradient(ellipse 400px 150px at 50% 50%, rgba(46,144,250,0.05), transparent);
      animation: fieldPulse 4s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { box-shadow: 0 0 20px rgba(46,144,250,0.3); }
      50% { box-shadow: 0 0 40px rgba(46,144,250,0.6); }
    }

    @keyframes fieldPulse {
      0%, 100% { opacity: 0.5; }
      50% { opacity: 1; }
    }

    /* Control Panels */
    .control-group {
      margin-bottom: 24px;
      background: rgba(255,255,255,0.02);
      border: 1px solid rgba(255,255,255,0.06);
      border-radius: 12px;
      padding: 20px;
    }

    .control-title {
      font-size: 14px;
      font-weight: 700;
      color: var(--accent);
      margin-bottom: 16px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .parameter-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .parameter-label {
      font-size: 12px;
      color: var(--soft);
      font-weight: 600;
    }

    .parameter-value {
      font-size: 14px;
      font-weight: 700;
      color: var(--accent);
      background: rgba(46,144,250,0.1);
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid rgba(46,144,250,0.2);
    }

    .slider-container {
      margin-top: 8px;
    }

    .slider {
      width: 100%;
      height: 6px;
      border-radius: 3px;
      background: rgba(255,255,255,0.1);
      appearance: none;
      outline: none;
      cursor: pointer;
    }

    .slider::-webkit-slider-thumb {
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--accent);
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(46,144,250,0.4);
    }

    /* Scan Button */
    .scan-button {
      width: 100%;
      padding: 16px;
      background: linear-gradient(135deg, var(--accent), var(--teal));
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(46,144,250,0.3);
      margin-top: 24px;
    }

    .scan-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(46,144,250,0.4);
    }

    .scan-button.scanning {
      background: linear-gradient(135deg, var(--warn), #ff6b35);
      animation: scanPulse 2s ease-in-out infinite;
    }

    @keyframes scanPulse {
      0%, 100% { box-shadow: 0 4px 16px rgba(245,158,11,0.3); }
      50% { box-shadow: 0 8px 32px rgba(245,158,11,0.6); }
    }

    /* Image Display */
    .image-display {
      background: #000;
      border-radius: 12px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid rgba(46,144,250,0.2);
    }

    .mri-image {
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, #2d3748 20%, #1a202c 80%);
      border-radius: 8px;
      position: relative;
      opacity: 0.8;
    }

    .brain-outline {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 200px;
      height: 180px;
      border: 2px solid rgba(46,144,250,0.6);
      border-radius: 50% 50% 45% 45%;
      background: linear-gradient(45deg, rgba(46,144,250,0.1), rgba(34,211,238,0.1));
    }

    .image-overlay {
      position: absolute;
      top: 16px;
      left: 16px;
      background: rgba(0,0,0,0.8);
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      color: var(--soft);
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="header-title">
      <div class="mri-icon">🧲</div>
      <div class="title-text">
        <h1>MRI Laboratory</h1>
        <p>Magnetic Resonance Imaging - Advanced Simulation</p>
      </div>
    </div>
    <div class="nav-actions">
      <a href="biomedical-imaging-instrumentation-splash.html" class="nav-btn">
        ← Back to Main Lab
      </a>
      <button class="nav-btn primary" onclick="startAdvancedScan()">
        Start Advanced Scan
      </button>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container">
    <!-- Left Panel: MRI Physics & Controls -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">MRI Physics & Parameters</div>
        <div class="panel-subtitle">Magnetic field and pulse sequence control</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Magnetic Field</div>
          <div class="parameter-item">
            <span class="parameter-label">Field Strength</span>
            <span class="parameter-value" id="field-strength">1.5T</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="0.5" max="3" step="0.5" value="1.5" 
                   onchange="updateField('strength', this.value)">
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Pulse Sequence</div>
          <div class="parameter-item">
            <span class="parameter-label">TE (Echo Time)</span>
            <span class="parameter-value" id="te-value">20ms</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="5" max="120" step="5" value="20" 
                   onchange="updateField('te', this.value)">
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">TR (Repetition Time)</span>
            <span class="parameter-value" id="tr-value">500ms</span>
          </div>
          <div class="slider-container">
            <input type="range" class="slider" min="100" max="3000" step="100" value="500" 
                   onchange="updateField('tr', this.value)">
          </div>
        </div>

        <button class="scan-button" id="scan-btn" onclick="toggleMRIScan()">
          Start MRI Scan
        </button>
      </div>
    </div>

    <!-- Center Panel: MRI Scanner Visualization -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">MRI Scanner</div>
        <div class="panel-subtitle">Real-time magnetic field visualization</div>
      </div>
      <div class="panel-content">
        <div class="mri-scanner">
          <div class="magnetic-field"></div>
          <div class="scanner-bore"></div>
          <div class="patient-table"></div>
        </div>
        
        <div class="image-display" style="margin-top: 24px; height: 350px;">
          <div class="image-overlay">
            T1-Weighted | Axial | Slice 15/30
          </div>
          <div class="mri-image">
            <div class="brain-outline"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel: Image Analysis -->
    <div class="panel">
      <div class="panel-header">
        <div class="panel-title">Image Analysis</div>
        <div class="panel-subtitle">Quality metrics and measurements</div>
      </div>
      <div class="panel-content">
        <div class="control-group">
          <div class="control-title">Image Quality</div>
          <div class="parameter-item">
            <span class="parameter-label">Signal-to-Noise Ratio</span>
            <span class="parameter-value" id="snr-display">85%</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Contrast Resolution</span>
            <span class="parameter-value" id="contrast-display">78%</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Spatial Resolution</span>
            <span class="parameter-value" id="resolution-display">1.2mm</span>
          </div>
        </div>

        <div class="control-group">
          <div class="control-title">Scan Progress</div>
          <div class="parameter-item">
            <span class="parameter-label">Acquisition Time</span>
            <span class="parameter-value" id="scan-time">00:00</span>
          </div>
          <div class="parameter-item">
            <span class="parameter-label">Slices Completed</span>
            <span class="parameter-value" id="slice-progress">0/30</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let isScanning = false;
    let scanProgress = 0;
    let scanTimer = null;

    function updateField(parameter, value) {
      const displays = {
        strength: 'field-strength',
        te: 'te-value', 
        tr: 'tr-value'
      };
      
      const units = {
        strength: 'T',
        te: 'ms',
        tr: 'ms'
      };
      
      document.getElementById(displays[parameter]).textContent = value + units[parameter];
      
      // Update visual effects based on parameters
      updateMRIVisualization(parameter, value);
    }

    function updateMRIVisualization(parameter, value) {
      const bore = document.querySelector('.scanner-bore');
      const field = document.querySelector('.magnetic-field');
      
      if (parameter === 'strength') {
        const intensity = value / 3; // Normalize to 0-1
        bore.style.borderColor = `rgba(46,144,250,${0.3 + intensity * 0.7})`;
        field.style.opacity = 0.3 + intensity * 0.7;
      }
    }

    function toggleMRIScan() {
      const button = document.getElementById('scan-btn');
      
      if (!isScanning) {
        startMRIScan();
        button.textContent = 'Stop Scan';
        button.classList.add('scanning');
      } else {
        stopMRIScan();
        button.textContent = 'Start MRI Scan';
        button.classList.remove('scanning');
      }
    }

    function startMRIScan() {
      isScanning = true;
      scanProgress = 0;
      let seconds = 0;
      
      scanTimer = setInterval(() => {
        seconds++;
        scanProgress = Math.min(100, (seconds / 30) * 100); // 30 second scan
        
        // Update displays
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        document.getElementById('scan-time').textContent = 
          `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        
        document.getElementById('slice-progress').textContent = 
          `${Math.floor(scanProgress / 100 * 30)}/30`;
        
        if (scanProgress >= 100) {
          completeMRIScan();
        }
      }, 1000);
    }

    function stopMRIScan() {
      isScanning = false;
      if (scanTimer) {
        clearInterval(scanTimer);
        scanTimer = null;
      }
      document.getElementById('scan-time').textContent = '00:00';
      document.getElementById('slice-progress').textContent = '0/30';
    }

    function completeMRIScan() {
      stopMRIScan();
      document.getElementById('scan-btn').textContent = 'Start MRI Scan';
      document.getElementById('scan-btn').classList.remove('scanning');
      
      // Show completion notification
      alert('MRI Scan completed successfully!');
    }

    function startAdvancedScan() {
      alert('Advanced MRI protocols will be available in the next update!');
    }
  </script>
</body>
</html>
