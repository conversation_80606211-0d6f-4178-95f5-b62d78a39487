<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>CT Reconstruction · Module 1</title>
  <link rel="stylesheet" href="../../assets/index-d7x91drb.css">
  <style>
    .container{max-width:960px;margin:0 auto;padding:24px}
    .card{background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:20px;margin:16px 0}
    header,footer{padding:12px 0}
    h1,h2,h3{color:#e6e9ff}
    p,li{color:#b9bfd6}
    .btn{display:inline-block;padding:8px 12px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b}
    .btn:hover{border-color:#3f4580;background:#1c2040}
    .grid-2{display:grid;grid-template-columns:1fr 1fr;gap:16px}
    .note{font-size:.9rem;color:#9fb1ff}
    code{background:#151933;border:1px solid #252a4a;border-radius:6px;padding:2px 6px}
  </style>
</head>
<body>
  <div class="container">
    <header>
      <a class="btn" href="./index.html">← Module 1 Index</a>
      <a class="btn" style="margin-left:8px" href="./mri.html">Previous: MRI</a>
      <h1>CT Reconstruction</h1>
      <p class="note">From projections to images: Radon transform, filtered backprojection and iterative methods.</p>
    </header>

    <section class="card">
      <h2>Learning Objectives</h2>
      <ul>
        <li>Explain how line integrals (projections) relate to object attenuation μ(x,y).</li>
        <li>Describe the Fourier Slice Theorem.</li>
        <li>Implement the basic steps of Filtered Backprojection (FBP).</li>
        <li>Recognize dose/image quality trade‑offs and common artifacts.</li>
      </ul>
    </section>

    <section class="card">
      <h2>Acquisition & Geometry</h2>
      <div class="grid-2">
        <div>
          <h3>Projections</h3>
          <p>Each detector reading is a line integral of attenuation along an x‑ray path: I = I0 e^{-∫ μ dl}. Taking logs yields line integrals p = -ln(I/I0).</p>
        </div>
        <div>
          <h3>Sinogram</h3>
          <p>Stacking projections versus angle θ forms a sinogram p(s,θ) that encodes the object’s Radon transform.</p>
        </div>
      </div>
    </section>

    <section class="card">
      <h2>Reconstruction Methods</h2>
      <div class="grid-2">
        <div>
          <h3>Filtered Backprojection (FBP)</h3>
          <ol>
            <li>1D FFT of each projection p(s,θ).</li>
            <li>Apply ramp filter |f| (with windowing: Hann, Hamming, Shepp‑Logan) to control noise.</li>
            <li>Inverse FFT to get filtered projections.</li>
            <li>Backproject over the image grid and sum over angles.</li>
          </ol>
        </div>
        <div>
          <h3>Iterative</h3>
          <ul>
            <li>ART/SIRT: update estimates using forward/back projections.</li>
            <li>Penalized likelihood with regularizers (TV) improves low‑dose CT.</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="card">
      <h2>Image Quality & Artifacts</h2>
      <ul>
        <li>Noise ∝ 1/√(mAs); spatial resolution set by detector/binning and focal spot.</li>
        <li>Beam hardening → cupping/streaks; correct with pre‑filtration and calibration.</li>
        <li>Metal artifacts → iterative recon, MAR algorithms.</li>
        <li>Motion → gating or faster rotation.</li>
      </ul>
    </section>

    <section class="card">
      <h2>Quick Quiz</h2>
      <ol>
        <li>State the Fourier Slice Theorem.</li>
        <li>Why is the ramp filter needed in FBP?</li>
        <li>Name one window applied to the ramp and its purpose.</li>
        <li>How does lowering mAs affect noise?</li>
        <li>Mention one cause and one mitigation for metal artifacts.</li>
      </ol>
      <p class="note">Answers: (1) 1D FT of a projection equals a central slice of the object’s 2D FT. (2) To counteract 1/r blurring of backprojection. (3) Hann/Hamming to reduce high‑freq noise. (4) Noise increases as 1/√mAs decreases. (5) High attenuation and photon starvation; use iterative recon/MAR.</p>
    </section>

    <footer>
      <a class="btn" href="./ultrasound.html">Next → Ultrasound</a>
    </footer>
  </div>
</body>
</html>
