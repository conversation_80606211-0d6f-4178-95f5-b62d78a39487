<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Module 2 · Imaging Detectors & Hardware</title>
  <link rel="stylesheet" href="../../assets/index-d7x91drb.css">
  <style>
    .container{max-width:1000px;margin:0 auto;padding:24px}
    .card{background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:20px;margin:16px 0}
    .grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:16px}
    .btn{display:inline-block;padding:10px 14px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b}
    .btn:hover{border-color:#3f4580;background:#1c2040}
    header,footer{padding:16px 0}
    h1,h2,h3{color:#e6e9ff}
    p,li{color:#b9bfd6}
    .badge{font-size:.8rem;color:#9fb1ff;background:#1a2350;border:1px solid #3040a0;border-radius:999px;padding:4px 10px}
    .pill{display:inline-block;font-size:.85rem;color:#9fb1ff;border:1px dashed #2c3670;border-radius:999px;padding:4px 10px;margin-right:6px}
    input[type="search"]{flex:1;min-width:220px;background:#0b0f1f;border:1px solid #1f2336;border-radius:10px;padding:10px;color:#e6e9ff}
  </style>
  <script>
    // Dynamic lessons for Module 2
    const lessons = [
      { id:"scintillators", title:"Scintillators & PMTs", summary:"Light conversion, quantum efficiency, coupling & gain.", href:"./scintillators.html", bullets:["NaI(Tl), LSO/LYSO","Photocathodes","Dynode gain","Energy resolution"]},
      { id:"transducers", title:"Ultrasound Transducers", summary:"Piezoelectric materials, arrays, matching & backing.", href:"./transducers.html", bullets:["PZT/CMUT/PMUT","Array geometry","Bandwidth","Axial/lateral res."]},
      { id:"frontend", title:"Analog Front-End (A/D & Timing)", summary:"LNAs, shaping, anti-aliasing, ADC selection and timing.", href:"./frontend.html", bullets:["Noise figure","Sample/hold","Clocking & jitter","Quantization"]},
      { id:"noise", title:"Noise & SNR", summary:"Noise sources, bandwidth tradeoffs and SNR budgeting.", href:"./noise.html", bullets:["Thermal/shot/1/f","Input referred","ENOB","Optimization"]}
    ];
    const $ = (q,r=document)=>r.querySelector(q);
    const $$ = (q,r=document)=>Array.from(r.querySelectorAll(q));
    function render(filter=""){
      const wrap = $("#lesson-grid"); wrap.innerHTML = "";
      const q = filter.trim().toLowerCase();
      lessons.filter(l=>!q || l.title.toLowerCase().includes(q) || l.summary.toLowerCase().includes(q) || l.bullets.join(" ").toLowerCase().includes(q))
        .forEach(l=>{
          const a = document.createElement("a");
          a.className="card btn";
          a.href=l.href;
          a.innerHTML = `
            <h3>${l.title}</h3>
            <p>${l.summary}</p>
            <div>${l.bullets.slice(0,3).map(b=>`<span class="pill">${b}</span>`).join("")}</div>
          `;
          wrap.appendChild(a);
        });
      $("#count").textContent = wrap.children.length;
    }
    function exportJSON(){
      const data = { module:"Imaging Detectors & Hardware", lessons };
      const blob = new Blob([JSON.stringify(data,null,2)], {type:"application/json"});
      const url = URL.createObjectURL(blob); const a = document.createElement("a");
      a.href=url; a.download="module-2.json"; a.click(); URL.revokeObjectURL(url);
    }
    document.addEventListener("DOMContentLoaded", ()=>{
      render();
      $("#search").addEventListener("input", e=>render(e.target.value));
      $("#export").addEventListener("click", exportJSON);
      $("#present").addEventListener("click", ()=>{ window.location.href="./present.html#module-2"; });
    });
  </script>
</head>
<body>
  <div class="container">
    <header>
      <a class="btn" href="../../index.html">← Back to Catalog</a>
      <h1>Module 2: Imaging Detectors & Hardware</h1>
      <p class="badge">Biomedical Imaging & Instrumentation Technology</p>
      <p>Explore detector physics, acoustic transduction, low-noise analog design, digitization and timing for imaging systems.</p>
    </header>

    <section class="card">
      <h2>Learning Objectives</h2>
      <ul>
        <li>Compare detector/transducer materials and performance metrics.</li>
        <li>Design analog front-ends balancing bandwidth, gain and noise.</li>
        <li>Quantify noise contributions and compute system SNR/ENOB.</li>
      </ul>
    </section>

    <section class="card">
      <h2>Lessons <span style="font-size:.9rem;color:#9fb1ff">( <span id="count">0</span> )</span></h2>
      <div style="display:flex;gap:8px;flex-wrap:wrap;margin-bottom:12px">
        <input id="search" type="search" placeholder="Search lessons or topics..."/>
        <button id="present" class="btn">Start Presentation</button>
        <button id="export" class="btn">Export JSON</button>
      </div>
      <div id="lesson-grid" class="grid"></div>
    </section>

    <footer>
      <p>Proceed through lessons; each includes key formulas, design tradeoffs, and a short quiz.</p>
    </footer>
  </div>
</body>
</html>
