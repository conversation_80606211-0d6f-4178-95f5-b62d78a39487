<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Module 2 Presentation · Detectors & Hardware</title>
  <link rel="stylesheet" href="../../assets/index-d7x91drb.css">
  <style>
    :root{--bg:#0b0f1f;--card:#0f1220;--muted:#a9b6d3;--accent:#5EEAD4;--pri:#2E90FA}
    html,body{height:100%}
    body{margin:0;background:radial-gradient(1200px 600px at 20% 0%, #0c1424 0%, var(--bg) 45%);color:#e6e9ff;font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif}
    .wrap{max-width:1100px;margin:0 auto;padding:18px}
    .bar{display:flex;align-items:center;justify-content:space-between;gap:10px}
    .bar .btn{padding:8px 12px;border-radius:10px;border:1px solid #293055;background:#131735;color:#e6e9ff;text-decoration:none}
    .bar .btn.primary{background:var(--pri);color:#061018;border-color:#2c72ff}
    .deck{margin-top:14px;background:linear-gradient(180deg,#0f1328,#0b0f20);border:1px solid #1f2336;border-radius:14px;min-height:480px;display:grid;grid-template-columns:280px 1fr}
    .sidebar{border-right:1px solid #1f2336;padding:12px;overflow:auto}
    .search{width:100%;background:#0b0f1f;border:1px solid #1f2336;border-radius:10px;padding:10px;color:#e6e9ff;margin-bottom:10px}
    .topic{display:block;background:#11172f;border:1px solid #1f2336;border-radius:10px;padding:10px;margin:8px 0;color:#cfe1ff}
    .topic.active{border-color:#2E90FA;background:#112044}
    .stage{padding:16px}
    .slide{display:none}
    .slide.active{display:block;animation:fade .25s ease}
    .card{background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:18px;margin:12px 0}
    .muted{color:#a9b6d3}
    .kbd{background:#151933;border:1px solid #252a4a;border-radius:6px;padding:2px 6px}
    .controls{display:flex;align-items:center;justify-content:space-between;gap:10px;margin-top:10px}
    .controls .btn{padding:10px 14px;border-radius:10px;border:1px solid #283050;background:#141a33;color:#e6e9ff}
    .controls .btn:hover{background:#1a2144}
    .prog{height:6px;border-radius:999px;background:#12162a;border:1px solid #1f2336;overflow:hidden}
    .prog > div{height:100%;background:linear-gradient(90deg,#2E90FA,#5EEAD4)}
    @keyframes fade{from{opacity:.6;transform:translateY(4px)}to{opacity:1;transform:none}}
    @media (max-width: 880px){.deck{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="bar">
      <div style="display:flex;gap:8px;align-items:center">
        <a class="btn" href="./index.html">← Module 2 Index</a>
        <strong>Interactive Presentation</strong>
        <span class="muted" id="lessonLabel"></span>
      </div>
      <div style="display:flex;gap:6px;align-items:center">
        <button class="btn" id="btnShuffle" title="Shuffle slides">Shuffle</button>
        <button class="btn primary" id="btnStart">Start</button>
      </div>
    </div>

    <div class="deck">
      <aside class="sidebar">
        <input id="filter" class="search" placeholder="Filter topics..."/>
        <nav id="toc"></nav>
      </aside>

      <main class="stage">
        <div id="slides"></div>

        <div class="controls">
          <div class="prog" style="flex:1"><div id="bar" style="width:0%"></div></div>
          <div style="display:flex;gap:6px">
            <button class="btn" id="prev">← Prev (←)</button>
            <button class="btn" id="next">Next (→) →</button>
          </div>
        </div>

        <p class="muted" style="margin:8px 0 0">Tips: Use keyboard arrows, <span class="kbd">F</span> for fullscreen, <span class="kbd">S</span> to shuffle.</p>
      </main>
    </div>
  </div>

  <script>
    const content = {
      scintillators: {
        title: "Scintillators & PMTs",
        slides: [
          {h:"Scintillation Basics", b:["Energy deposition → photons","Light yield (ph/keV), decay time"]},
          {h:"Materials", b:["NaI(Tl): high light, slow","LSO/LYSO: fast, dense, PET"]},
          {h:"Photomultipliers", b:["QE of photocathode","Dynode gain G≈δ^n"]},
          {h:"Resolution", b:["FWHM/E ∝ 1/√N_photons","Coupling & reflectors matter"]}
        ]
      },
      transducers: {
        title: "Ultrasound Transducers",
        slides: [
          {h:"Piezoelectricity", b:["PZT vs CMUT/PMUT","f0≈c/(2t), bandwidth via backing"]},
          {h:"Arrays", b:["Linear, phased, curvilinear","Grating lobes ← pitch > λ/2"]},
          {h:"Matching", b:["Quarter‑wave layer","Impedance matching improves SNR"]}
        ]
      },
      frontend: {
        title: "Analog Front-End (A/D & Timing)",
        slides: [
          {h:"LNA & Shaping", b:["Set bandwidth by signal", "Noise figure minimization"]},
          {h:"Anti‑aliasing & ADC", b:["fc < fs/2","ENOB vs SNR, SFDR"]},
          {h:"Timing", b:["Clock jitter → SNR_jitter ≈ −20log(2π f_in σ_j) dB","TDC/ToF basics"]}
        ]
      },
      noise: {
        title: "Noise & SNR",
        slides: [
          {h:"Noise Sources", b:["Thermal: √(4kTRB)", "Shot: √(2qIB)", "1/f at low freq"]},
          {h:"Input‑Referred", b:["Refer all blocks to input","Bandwidth shaping"]},
          {h:"Budgeting", b:["Cascade Friis for NF","Optimize gain distribution"]}
        ]
      }
    };

    const keys = Object.keys(content);
    let current = keys[0], slides = [], i = 0;
    const $ = (q,r=document)=>r.querySelector(q);
    const $$ = (q,r=document)=>Array.from(r.querySelectorAll(q));

    function buildTOC(){
      const nav = $("#toc"); nav.innerHTML="";
      keys.forEach(k=>{
        const a = document.createElement("a");
        a.href="#"+k; a.className="topic"+(k===current?" active":""); a.textContent=content[k].title;
        a.addEventListener("click",(e)=>{e.preventDefault(); select(k);});
        nav.appendChild(a);
      });
    }
    function select(k){
      current = k;
      $$("#toc .topic").forEach(x=>x.classList.toggle("active", x.textContent===content[k].title));
      $("#lessonLabel").textContent = "• "+content[k].title;
      slides = content[k].slides.map(s=>({...s})); i=0; render();
    }
    function render(){
      const host = $("#slides"); host.innerHTML="";
      slides.forEach((s,idx)=>{
        const el = document.createElement("section");
        el.className="slide card"+(idx===i?" active":"");
        el.innerHTML = `<h2 style="margin:0 0 8px">${s.h}</h2>
          <ul style="margin:8px 0 0 18px;color:#cfe1ff">${s.b.map(x=>`<li>${x}</li>`).join("")}</ul>`;
        host.appendChild(el);
      });
      updateBar();
    }
    function updateBar(){ const pct = slides.length? ((i+1)/slides.length)*100:0; $("#bar").style.width=pct+"%"; $$(".slide").forEach((s,idx)=>s.classList.toggle("active", idx===i)); }
    function next(){ if(i<slides.length-1){ i++; updateBar(); } }
    function prev(){ if(i>0){ i--; updateBar(); } }
    function shuffle(){ for(let j=slides.length-1;j>0;j--){ const k=Math.floor(Math.random()*(j+1)); [slides[j],slides[k]]=[slides[k],slides[j]] } i=0; render(); }

    document.addEventListener("keydown",(e)=>{ if(e.key==="ArrowRight") next(); else if(e.key==="ArrowLeft") prev(); else if(e.key.toLowerCase()==="s") shuffle(); else if(e.key.toLowerCase()==="f"){ if(!document.fullscreenElement) document.documentElement.requestFullscreen().catch(()=>{}); else document.exitFullscreen(); }});
    $("#next").addEventListener("click", next);
    $("#prev").addEventListener("click", prev);
    $("#btnShuffle").addEventListener("click", shuffle);
    $("#btnStart").addEventListener("click", ()=>{ i=0; updateBar(); });

    $("#filter").addEventListener("input", (e)=>{ const q=e.target.value.toLowerCase(); $$("#toc .topic").forEach(a=>{ a.style.display = a.textContent.toLowerCase().includes(q) ? "" : "none"; }); });

    buildTOC(); select(current);
  </script>
</body>
</html>
