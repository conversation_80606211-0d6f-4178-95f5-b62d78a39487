<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>Biomedical Imaging & Instrumentation Technology — Interactive Learning Module</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800;900&display=swap" rel="stylesheet">
<style>
:root{
  --bg:#101828;        /* dark base */
  --panel:#0B1220;
  --panel-2:#111A2C;
  --text:#E5E7EB;
  --soft:#CBD5E1;
  --muted:#6B7280;
  --accent:#2E90FA;    /* blue */
  --teal:#22D3EE;      /* teal */
  --cyan:#67E8F9;      /* CT cyan */
  --mri:#BAE6FD;       /* MRI cool white/blue */
  --us:#86EFAC;        /* Ultrasound light green */
  --pet1:#FDE047;      /* PET yellow */
  --pet2:#FB923C;      /* PET orange */
  --pet3:#EF4444;      /* PET red */
  --ok:#10B981;
  --warn:#F59E0B;
  --glow: 0 30px 80px rgba(0,0,0,.6), inset 0 1px 0 rgba(255,255,255,.03);
  --card: rgba(255,255,255,0.02);
  --card-b: rgba(255,255,255,0.08);
  --radius:14px;
  --gap:14px;
  --shadow: 0 10px 30px rgba(0,0,0,.45), inset 0 1px 0 rgba(255,255,255,.02);
}
*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0; font-family:Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; color:var(--text);
  background:
    radial-gradient(1200px 900px at 70% -10%, rgba(46,144,250,0.07), transparent 60%),
    radial-gradient(900px 700px at 15% 110%, rgba(34,211,238,0.06), transparent 60%),
    var(--bg);
}

/* Top Navigation Bar */
.topbar{
  height:56px; padding:0 18px; display:flex; align-items:center; justify-content:space-between;
  border-bottom:1px solid rgba(255,255,255,0.06);
  background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0));
  backdrop-filter: blur(6px);
}
.brand{ display:flex; align-items:center; gap:10px; font-weight:800; font-size:18px; }
.brand-badge{
  width:28px; height:28px; border-radius:8px;
  background:linear-gradient(135deg,var(--accent),var(--teal));
  box-shadow: 0 6px 16px rgba(46,144,250,.35);
  display:grid; place-items:center; color:white; font-weight:900; font-size:12px;
}
.nav-actions{ display:flex; gap:8px; }
.nav-btn{
  padding:8px 12px; border-radius:10px; border:1px solid rgba(255,255,255,0.08);
  background:var(--card); color:var(--text); cursor:pointer; font-size:12px;
  transition: all 0.2s ease;
}
.nav-btn:hover{ border-color:var(--accent); background:rgba(46,144,250,0.1); }
.nav-btn.active{ background:var(--accent); color:white; }

/* Main Container - Two Panel Layout */
.container{
  height: calc(100% - 56px);
  display:grid;
  grid-template-columns: 400px 1fr; /* Left panel fixed, right panel flexible */
  gap: var(--gap);
  padding: var(--gap);
}

/* Left Panel - Content/Theory */
.content-panel{
  background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
  border:1px solid rgba(255,255,255,0.06); border-radius:var(--radius);
  box-shadow:var(--shadow);
  display:flex; flex-direction:column; overflow:hidden;
}
.panel-header{
  padding:16px 18px; border-bottom:1px solid rgba(255,255,255,0.06);
  display:flex; align-items:center; justify-content:space-between;
}
.panel-title{ font-weight:800; font-size:16px; letter-spacing:.2px; }
.panel-subtitle{ font-size:12px; color:var(--muted); margin-top:2px; }
.panel-content{
  flex:1; overflow-y:auto; padding:18px;
}

/* Right Panel - Interactive Lab */
.lab-panel{
  background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0)), var(--panel);
  border:1px solid rgba(255,255,255,0.06); border-radius:var(--radius);
  box-shadow:var(--shadow);
  display:flex; flex-direction:column; overflow:hidden;
}

/* Content Styling */
.section{
  margin-bottom:24px; padding-bottom:20px; border-bottom:1px solid rgba(255,255,255,0.06);
}
.section:last-child{ border-bottom:none; margin-bottom:0; }
.section-title{
  font-weight:700; font-size:14px; color:var(--accent); margin-bottom:12px;
  display:flex; align-items:center; gap:8px;
}
.section-icon{
  width:20px; height:20px; border-radius:6px; background:var(--accent);
  display:grid; place-items:center; color:white; font-size:10px; font-weight:900;
}
.section-content{
  font-size:13px; line-height:1.6; color:var(--soft);
}
.section-content p{ margin:0 0 12px 0; }
.section-content ul{ margin:8px 0; padding-left:16px; }
.section-content li{ margin:4px 0; }
.highlight{ color:var(--teal); font-weight:600; }
.emphasis{ color:var(--accent); font-weight:600; }

/* Interactive Controls */
.controls{
  padding:16px 18px; border-top:1px solid rgba(255,255,255,0.06);
  background: rgba(255,255,255,0.01);
}
.control-group{
  margin-bottom:16px;
}
.control-group:last-child{ margin-bottom:0; }
.control-label{
  font-size:12px; font-weight:600; color:var(--soft); margin-bottom:8px;
  display:block;
}
.control-row{
  display:flex; align-items:center; gap:12px; margin-bottom:8px;
}
.slider{
  flex:1; height:6px; border-radius:3px; background:rgba(255,255,255,0.1);
  appearance:none; outline:none; cursor:pointer;
}
.slider::-webkit-slider-thumb{
  appearance:none; width:16px; height:16px; border-radius:50%;
  background:var(--accent); cursor:pointer;
  box-shadow: 0 2px 8px rgba(46,144,250,0.4);
}
.slider::-moz-range-thumb{
  width:16px; height:16px; border-radius:50%; background:var(--accent);
  border:none; cursor:pointer; box-shadow: 0 2px 8px rgba(46,144,250,0.4);
}
.value-display{
  font-size:11px; color:var(--muted); min-width:40px; text-align:right;
}
.btn{
  padding:8px 12px; border-radius:8px; border:1px solid rgba(255,255,255,0.08);
  background:var(--card); color:var(--text); cursor:pointer; font-size:12px;
  transition: all 0.2s ease; font-weight:600;
}
.btn:hover{ border-color:var(--accent); background:rgba(46,144,250,0.1); }
.btn.primary{ background:var(--accent); color:white; border-color:var(--accent); }
.btn.primary:hover{ background:rgba(46,144,250,0.8); }

/* Lab Canvas */
.lab-canvas{
  flex:1; position:relative; overflow:hidden;
  background:
    radial-gradient(1200px 800px at 50% -20%, rgba(46,144,250,0.06), transparent 70%),
    linear-gradient(180deg, #0C1426, #0A1122 60%, #091024);
}

/* Status Indicators */
.status-bar{
  display:flex; gap:12px; margin-bottom:12px;
}
.status{
  padding:4px 8px; border-radius:6px; font-size:11px; font-weight:600;
  border:1px solid;
}
.status.active{ background:var(--ok); color:white; border-color:var(--ok); }
.status.inactive{ background:rgba(107,114,128,0.2); color:var(--muted); border-color:var(--muted); }
.status.warning{ background:var(--warn); color:white; border-color:var(--warn); }

/* Visualization Elements */
.figure-wrap{
  position:absolute; left:50%; top:52%; transform:translate(-50%,-50%);
  width:520px; height:820px; pointer-events:none;
  filter: drop-shadow(0 20px 60px rgba(34,211,238,.25));
}
.holo-figure{
  position:absolute; inset:0; border-radius:22px;
  background:
    radial-gradient(240px 420px at 50% 20%, rgba(34,211,238,0.12), transparent 60%),
    radial-gradient(260px 520px at 50% 65%, rgba(46,144,250,0.10), transparent 66%),
    linear-gradient(180deg, rgba(34,197,255,0.18), rgba(34,211,238,0.06));
  opacity:.9; mix-blend-mode:screen;
}
.holo-wire{
  position:absolute; inset:0; border-radius:22px;
  background-image:
    linear-gradient(to right, rgba(103,232,249,.15) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(103,232,249,.15) 1px, transparent 1px);
  background-size: 22px 22px, 22px 22px; opacity:.35;
  mask-image: radial-gradient(60% 70% at 50% 45%, black 55%, transparent 80%);
}
.skeleton{
  position:absolute; left:50%; top:50%; transform:translate(-50%,-50%);
  width:320px; height:620px; border-radius:18px;
  background:
    radial-gradient(80px 160px at 50% 20%, rgba(190,227,248,.5), rgba(14,165,233,.1) 70%, transparent 80%),
    radial-gradient(140px 260px at 50% 60%, rgba(148,163,184,.35), rgba(15,23,42,0) 70%),
    linear-gradient(180deg, rgba(148,163,184,.25), rgba(14,116,144,.15));
  box-shadow: inset 0 0 40px rgba(0,0,0,.4);
  filter: blur(.2px) saturate(1.05);
}
.organ{
  position:absolute; border-radius:20px; opacity:.75;
  box-shadow: 0 10px 24px rgba(0,0,0,.35), inset 0 0 30px rgba(255,255,255,.06);
}
.brain{
  left:50%; top:22%; transform:translate(-50%,-50%); width:120px; height:90px;
  background: radial-gradient(60px 40px at 50% 50%, rgba(186,230,253,.9), rgba(30,64,175,.35));
}
.heart{
  left:50%; top:38%; transform:translate(-50%,-50%); width:70px; height:80px; border-radius:32px;
  background: radial-gradient(40px 40px at 50% 50%, rgba(244,114,182,.65), rgba(56,189,248,.25));
}
.lungs{
  left:50%; top:34%; transform:translate(-50%,-50%); width:180px; height:120px; border-radius:60px;
  background: radial-gradient(90px 60px at 50% 50%, rgba(125,211,252,.5), rgba(2,132,199,.25));
}
.liver{
  left:55%; top:48%; transform:translate(-50%,-50%); width:140px; height:70px; border-radius:40px;
  background: radial-gradient(70px 40px at 50% 50%, rgba(45,212,191,.55), rgba(15,118,110,.25));
}

/* MRI Rings */
.mri-rings{
  position:absolute; left:50%; top:22%; transform:translate(-50%,-50%); width:220px; height:220px; border-radius:50%;
  pointer-events:none;
}
.ring{
  position:absolute; inset:0; border-radius:50%;
  border:2px solid rgba(186,230,253,.65);
  box-shadow: 0 0 22px rgba(186,230,253,.45), inset 0 0 22px rgba(186,230,253,.25);
  animation: sweep 4s linear infinite;
}
.ring.r2{ transform: scale(.82); animation-delay:.3s; opacity:.85 }
.ring.r3{ transform: scale(.64); animation-delay:.6s; opacity:.7 }
@keyframes sweep {
  0%{ filter: hue-rotate(0deg) brightness(1); opacity:.9 }
  50%{ filter: hue-rotate(8deg) brightness(1.08); opacity:.7 }
  100%{ filter: hue-rotate(0deg) brightness(1); opacity:.9 }
}

/* CT Elements */
.ct-gantry{
  position:absolute; left:50%; top:40%; transform:translate(-50%,-50%); width:400px; height:400px; border-radius:50%;
  border:1px dashed rgba(103,232,249,.25); box-shadow: inset 0 0 40px rgba(103,232,249,.08);
}
.ct-beam{
  position:absolute; left:50%; top:50%; width:420px; height:2px;
  background: linear-gradient(90deg, transparent 0%, rgba(103,232,249,.9) 40%, rgba(34,211,238,.9) 60%, transparent 100%);
  transform-origin: left center; transform: rotate(0deg);
  filter: drop-shadow(0 0 10px rgba(103,232,249,.7));
  animation: rotate 6s linear infinite;
}
@keyframes rotate { to { transform: rotate(360deg) } }

/* CT HUD */
.hud{
  position:absolute; top:16%; right:3%; width:280px; border-radius:12px; overflow:hidden;
  background: rgba(10,16,36,.6); border:1px solid rgba(255,255,255,.08);
  backdrop-filter: blur(6px); box-shadow: var(--glow);
}
.hud-head{
  display:flex; align-items:center; justify-content:space-between; padding:10px 12px; font-size:12px; color:var(--soft);
  border-bottom:1px solid rgba(255,255,255,.06);
}
.slices{
  padding:10px; display:grid; grid-template-columns: repeat(4, 1fr); gap:6px;
}
.slice{
  position:relative; height:50px; border-radius:6px; overflow:hidden; border:1px solid rgba(103,232,249,.25);
  background:
    linear-gradient(180deg, rgba(2,132,199,.08), rgba(103,232,249,.12)),
    repeating-linear-gradient(0deg, rgba(103,232,249,.12), rgba(103,232,249,.12) 2px, transparent 2px, transparent 4px);
}
.slice::after{
  content:""; position:absolute; inset:0;
  background: radial-gradient(120px 50px at 60% 50%, rgba(103,232,249,.25), transparent 70%);
}

/* Ultrasound Elements */
.us-probe{
  position:absolute; left:35%; top:56%; width:80px; height:44px; border-radius:10px 10px 18px 18px / 10px 10px 18px 18px;
  background: linear-gradient(180deg,#F3F4F6,#CBD5E1); border:1px solid #94A3B8;
  box-shadow: 0 10px 24px rgba(0,0,0,.35), inset 0 -2px 0 rgba(0,0,0,.1);
  transform: rotate(-12deg);
}
.us-wave{
  position:absolute; left:40%; top:57%; width:160px; height:160px; border-radius:50%;
  border:2px solid rgba(134,239,172,.55);
  box-shadow: 0 0 18px rgba(134,239,172,.4), inset 0 0 18px rgba(134,239,172,.2);
  animation: ripple 2.6s ease-out infinite;
}
.us-wave.w2{ transform: scale(1.25); animation-delay:.3s; opacity:.8 }
.us-wave.w3{ transform: scale(1.5); animation-delay:.6s; opacity:.6 }
@keyframes ripple {
  0%{ transform: scale(0.9); opacity:.85 }
  100%{ transform: scale(1.8); opacity:0 }
}

/* Ultrasound HUD */
.hud-us{
  position:absolute; bottom:6%; left:4%; width:300px; border-radius:12px; overflow:hidden;
  background: rgba(10,16,36,.6); border:1px solid rgba(255,255,255,.08);
  -webkit-backdrop-filter: blur(6px); backdrop-filter: blur(6px); box-shadow: var(--glow);
}
.us-head{
  padding:10px 12px; font-size:12px; color:var(--soft);
  border-bottom:1px solid rgba(255,255,255,.06)
}
.us-body{
  padding:10px; display:grid; grid-template-columns: repeat(6, 1fr); gap:2px;
}
.us-px{
  width:100%; height:14px; background: #111827;
  background: linear-gradient(180deg, #2D2F31, #0B0C0E);
  filter: contrast(1.6) brightness(.9);
}
.us-px:nth-child(3n){ filter: brightness(1.15) }
.us-px:nth-child(7n){ filter: brightness(.7) }

/* PET Elements */
.pet-hotspot{
  position:absolute; left:52%; top:38%; transform:translate(-50%,-50%);
  width:64px; height:64px; border-radius:50%;
  background: radial-gradient(circle at 50% 50%, var(--pet1), var(--pet2) 60%, var(--pet3) 100%);
  box-shadow:
    0 0 20px rgba(253,224,71,.75),
    0 0 34px rgba(251,146,60,.55),
    0 0 46px rgba(239,68,68,.45);
  animation: pulse 1.8s ease-in-out infinite;
}
@keyframes pulse {
  0%,100%{ transform:translate(-50%,-50%) scale(1) }
  50%{ transform:translate(-50%,-50%) scale(1.15) }
}
.spark{
  position:absolute; width:3px; height:3px; border-radius:50%; background: #FCA311;
  filter: drop-shadow(0 0 6px rgba(255,196,0,.8));
  animation: drift 1.6s ease-out infinite;
}
.spark.s2{ animation-delay:.3s }
.spark.s3{ animation-delay:.6s }
@keyframes drift{
  0%{ transform: translate(0,0); opacity:1 }
  100%{ transform: translate(20px,-28px); opacity:0 }
}

/* Labels and Lines */
.label{
  position:absolute; font-size:12px; color:var(--soft);
  -webkit-backdrop-filter: blur(4px); backdrop-filter: blur(4px);
  background: rgba(8,13,28,.55); border:1px solid rgba(255,255,255,.08);
  padding:6px 8px; border-radius:8px; box-shadow: var(--glow);
}
.line{
  position:absolute; width:2px;
  background: linear-gradient(180deg, rgba(46,144,250,0), rgba(46,144,250,.7));
  box-shadow: 0 0 10px rgba(46,144,250,.35);
}

/* Background Elements */
.stream{
  position:absolute; inset:0; pointer-events:none; opacity:.18;
  background-image:
    linear-gradient(to right, rgba(46,144,250,.25) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(46,144,250,.15) 1px, transparent 1px);
  background-size: 40px 40px, 40px 40px;
  animation: pan 40s linear infinite;
}
@keyframes pan {
  from{ background-position: 0 0, 0 0 }
  to{ background-position: 400px 0, 0 400px }
}

/* Hardware Icons */
.hardware{
  position:absolute; right:24px; bottom:24px; display:flex; gap:10px;
}
.hw{
  width:60px; height:60px; border-radius:12px; border:1px solid rgba(255,255,255,.08);
  background: rgba(255,255,255,.03);
  display:grid; place-items:center; color:#BFE4FF; font-weight:900; font-size:12px;
  box-shadow: var(--glow);
  animation: spin 10s linear infinite;
}
@keyframes spin { to { transform: rotate(360deg) } }

/* Enhanced Interactive Controls */
.monitor-dashboard{
  background: rgba(255,255,255,0.02); border: 1px solid rgba(255,255,255,0.06);
  border-radius: 10px; padding: 12px; margin-bottom: 16px;
}
.monitor-group{
  display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px;
}
.monitor-item{
  background: rgba(255,255,255,0.01); border: 1px solid rgba(255,255,255,0.04);
  border-radius: 8px; padding: 10px;
}
.monitor-label{
  font-size: 11px; font-weight: 600; color: var(--accent); margin-bottom: 8px;
  text-transform: uppercase; letter-spacing: 0.5px;
}
.vital-display{
  display: flex; flex-direction: column; gap: 4px;
}
.vital-item{
  font-size: 12px; color: var(--soft); display: flex; justify-content: space-between;
}
.quality-meters{
  display: flex; flex-direction: column; gap: 6px;
}
.meter{
  display: flex; align-items: center; gap: 8px; font-size: 11px;
}
.meter-bar{
  flex: 1; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; overflow: hidden;
}
.meter-fill{
  height: 100%; background: linear-gradient(90deg, var(--ok), var(--accent)); transition: width 0.3s ease;
}
.scan-display{
  display: flex; flex-direction: column; gap: 8px;
}
.scan-progress{
  display: flex; align-items: center; gap: 8px;
}
.progress-bar{
  flex: 1; height: 8px; background: rgba(255,255,255,0.1); border-radius: 4px; overflow: hidden;
}
.progress-fill{
  height: 100%; background: linear-gradient(90deg, var(--accent), var(--teal));
  transition: width 0.3s ease; border-radius: 4px;
}
.scan-btn{
  padding: 8px 16px; border-radius: 8px; border: 1px solid var(--accent);
  background: var(--accent); color: white; cursor: pointer; font-weight: 600;
  transition: all 0.2s ease; font-size: 12px;
}
.scan-btn:hover{ background: rgba(46,144,250,0.8); }
.scan-btn.scanning{ background: var(--warn); border-color: var(--warn); }

.modality-selector{
  display: flex; gap: 8px; margin-bottom: 16px; padding: 8px;
  background: rgba(255,255,255,0.02); border-radius: 10px;
}
.modality-btn{
  flex: 1; padding: 12px 8px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.08);
  background: rgba(255,255,255,0.02); color: var(--soft); cursor: pointer;
  transition: all 0.2s ease; display: flex; flex-direction: column; align-items: center; gap: 4px;
}
.modality-btn:hover{ border-color: var(--accent); background: rgba(46,144,250,0.1); }
.modality-btn.active{ background: var(--accent); color: white; border-color: var(--accent); }
.modality-icon{ font-size: 16px; }

.parameter-controls{
  max-height: 400px; overflow-y: auto;
}
.control-panel{
  background: rgba(255,255,255,0.01); border: 1px solid rgba(255,255,255,0.06);
  border-radius: 10px; padding: 16px; margin-bottom: 16px;
}
.panel-header-small{
  display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;
  padding-bottom: 12px; border-bottom: 1px solid rgba(255,255,255,0.06);
}
.panel-header-small h4{
  margin: 0; font-size: 14px; font-weight: 700; color: var(--accent);
}
.param-select{
  padding: 6px 10px; border-radius: 6px; border: 1px solid rgba(255,255,255,0.08);
  background: rgba(255,255,255,0.02); color: var(--text); font-size: 12px;
}
.param-grid{
  display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;
}
.param-group{
  display: flex; flex-direction: column; gap: 6px;
}
.param-label{
  font-size: 12px; font-weight: 600; color: var(--soft);
}
.param-control{
  display: flex; align-items: center; gap: 10px;
}
.param-slider{
  flex: 1; height: 6px; border-radius: 3px; background: rgba(255,255,255,0.1);
  appearance: none; outline: none; cursor: pointer;
}
.param-slider::-webkit-slider-thumb{
  appearance: none; width: 16px; height: 16px; border-radius: 50%;
  background: var(--accent); cursor: pointer; box-shadow: 0 2px 8px rgba(46,144,250,0.4);
}
.param-value{
  font-size: 11px; color: var(--accent); min-width: 50px; text-align: right; font-weight: 600;
}
.param-effect{
  font-size: 10px; color: var(--muted); font-style: italic;
}
.real-time-feedback{
  margin-top: 16px; padding-top: 12px; border-top: 1px solid rgba(255,255,255,0.06);
  display: flex; gap: 16px; flex-wrap: wrap;
}
.feedback-item{
  display: flex; flex-direction: column; gap: 2px;
}
.feedback-item span:first-child{
  font-size: 10px; color: var(--muted); text-transform: uppercase;
}
.feedback-value{
  font-size: 12px; font-weight: 600; color: var(--teal);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container{
    grid-template-columns: 350px 1fr;
  }
}
@media (max-width: 900px) {
  .container{
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  .content-panel{
    max-height: 300px;
  }
}

/* Central holographic figure */
.figure-wrap{
  position:absolute; left:50%; top:52%; transform:translate(-50%,-50%);
  width:520px; height:820px; pointer-events:none;
  filter: drop-shadow(0 20px 60px rgba(34,211,238,.25));
}
.holo-figure{
  position:absolute; inset:0; border-radius:22px;
  background:
    radial-gradient(240px 420px at 50% 20%, rgba(34,211,238,0.12), transparent 60%),
    radial-gradient(260px 520px at 50% 65%, rgba(46,144,250,0.10), transparent 66%),
    linear-gradient(180deg, rgba(34,197,255,0.18), rgba(34,211,238,0.06));
  opacity:.9; mix-blend-mode:screen;
}
.holo-wire{
  position:absolute; inset:0; border-radius:22px;
  background-image:
    linear-gradient(to right, rgba(103,232,249,.15) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(103,232,249,.15) 1px, transparent 1px);
  background-size: 22px 22px, 22px 22px; opacity:.35; mask-image: radial-gradient(60% 70% at 50% 45%, black 55%, transparent 80%);
}
/* Skeleton and organs (stylized translucent) */
.skeleton{
  position:absolute; left:50%; top:50%; transform:translate(-50%,-50%);
  width:320px; height:620px; border-radius:18px;
  background:
    radial-gradient(80px 160px at 50% 20%, rgba(190,227,248,.5), rgba(14,165,233,.1) 70%, transparent 80%),
    radial-gradient(140px 260px at 50% 60%, rgba(148,163,184,.35), rgba(15,23,42,0) 70%),
    linear-gradient(180deg, rgba(148,163,184,.25), rgba(14,116,144,.15));
  box-shadow: inset 0 0 40px rgba(0,0,0,.4);
  filter: blur(.2px) saturate(1.05);
}
/* organs */
.organ{
  position:absolute; border-radius:20px; opacity:.75; box-shadow: 0 10px 24px rgba(0,0,0,.35), inset 0 0 30px rgba(255,255,255,.06);
}
.brain{ left:50%; top:22%; transform:translate(-50%,-50%); width:120px; height:90px; background: radial-gradient(60px 40px at 50% 50%, rgba(186,230,253,.9), rgba(30,64,175,.35)); }
.heart{ left:50%; top:38%; transform:translate(-50%,-50%); width:70px; height:80px; border-radius:32px; background: radial-gradient(40px 40px at 50% 50%, rgba(244,114,182,.65), rgba(56,189,248,.25)); }
.lungs{ left:50%; top:34%; transform:translate(-50%,-50%); width:180px; height:120px; border-radius:60px; background: radial-gradient(90px 60px at 50% 50%, rgba(125,211,252,.5), rgba(2,132,199,.25)); }
.liver{ left:55%; top:48%; transform:translate(-50%,-50%); width:140px; height:70px; border-radius:40px; background: radial-gradient(70px 40px at 50% 50%, rgba(45,212,191,.55), rgba(15,118,110,.25)); }

/* MRI around head: concentric gradient arcs */
.mri-rings{
  position:absolute; left:50%; top:22%; transform:translate(-50%,-50%); width:220px; height:220px; border-radius:50%;
  pointer-events:none;
}
.ring{
  position:absolute; inset:0; border-radius:50%;
  border:2px solid rgba(186,230,253,.65);
  box-shadow: 0 0 22px rgba(186,230,253,.45), inset 0 0 22px rgba(186,230,253,.25);
  animation: sweep 4s linear infinite;
}
.ring.r2{ transform: scale(.82); animation-delay:.3s; opacity:.85 }
.ring.r3{ transform: scale(.64); animation-delay:.6s; opacity:.7 }
@keyframes sweep {
  0%{ filter: hue-rotate(0deg) brightness(1); opacity:.9 }
  50%{ filter: hue-rotate(8deg) brightness(1.08); opacity:.7 }
  100%{ filter: hue-rotate(0deg) brightness(1); opacity:.9 }
}

/* CT rotating beam around torso */
.ct-gantry{
  position:absolute; left:50%; top:40%; transform:translate(-50%,-50%); width:400px; height:400px; border-radius:50%;
  border:1px dashed rgba(103,232,249,.25); box-shadow: inset 0 0 40px rgba(103,232,249,.08);
}
.ct-beam{
  --angle: 0deg;
  position:absolute; left:50%; top:50%; width:420px; height:2px; background: linear-gradient(90deg, transparent 0%, rgba(103,232,249,.9) 40%, rgba(34,211,238,.9) 60%, transparent 100%);
  transform-origin: left center;
  transform: rotate(var(--angle));
  filter: drop-shadow(0 0 10px rgba(103,232,249,.7));
  animation: rotate 6s linear infinite;
}
@keyframes rotate { to { transform: rotate(360deg) } }

/* CT slices HUD */
.hud{
  position:absolute; top:16%; right:3%; width:280px; border-radius:12px; overflow:hidden;
  background: rgba(10,16,36,.6); border:1px solid rgba(255,255,255,.08); backdrop-filter: blur(6px); -webkit-backdrop-filter: blur(6px);
  box-shadow: var(--glow);
}
.hud-head{
  display:flex; align-items:center; justify-content:space-between; padding:10px 12px; font-size:12px; color:var(--soft);
  border-bottom:1px solid rgba(255,255,255,.06);
}
.slices{
  padding:10px; display:grid; grid-template-columns: repeat(4, 1fr); gap:6px;
}
.slice{
  position:relative; height:50px; border-radius:6px; overflow:hidden; border:1px solid rgba(103,232,249,.25);
  background:
    linear-gradient(180deg, rgba(2,132,199,.08), rgba(103,232,249,.12)),
    repeating-linear-gradient(0deg, rgba(103,232,249,.12), rgba(103,232,249,.12) 2px, transparent 2px, transparent 4px);
}
.slice::after{
  content:""; position:absolute; inset:0; background: radial-gradient(120px 50px at 60% 50%, rgba(103,232,249,.25), transparent 70%);
}

/* Ultrasound probe & waves */
.us-probe{
  position:absolute; left:35%; top:56%; width:80px; height:44px; border-radius:10px 10px 18px 18px / 10px 10px 18px 18px;
  background: linear-gradient(180deg,#F3F4F6,#CBD5E1); border:1px solid #94A3B8; box-shadow: 0 10px 24px rgba(0,0,0,.35), inset 0 -2px 0 rgba(0,0,0,.1);
  transform: rotate(-12deg);
}
.us-wave{
  position:absolute; left:40%; top:57%; width:160px; height:160px; border-radius:50%;
  border:2px solid rgba(134,239,172,.55); box-shadow: 0 0 18px rgba(134,239,172,.4), inset 0 0 18px rgba(134,239,172,.2);
  animation: ripple 2.6s ease-out infinite;
}
.us-wave.w2{ transform: scale(1.25); animation-delay:.3s; opacity:.8 }
.us-wave.w3{ transform: scale(1.5); animation-delay:.6s; opacity:.6 }
@keyframes ripple {
  0%{ transform: scale(0.9); opacity:.85 }
  100%{ transform: scale(1.8); opacity:0 }
}
/* Ultrasound HUD */
.hud-us{
  position:absolute; bottom:6%; left:4%; width:300px; border-radius:12px; overflow:hidden;
  background: rgba(10,16,36,.6); border:1px solid rgba(255,255,255,.08); backdrop-filter: blur(6px); -webkit-backdrop-filter: blur(6px);
  box-shadow: var(--glow);
}
.us-head{ padding:10px 12px; font-size:12px; color:var(--soft); border-bottom:1px solid rgba(255,255,255,.06) }
.us-body{ padding:10px; display:grid; grid-template-columns: repeat(6, 1fr); gap:2px; }
.us-px{
  width:100%; height:14px; background: #111827;
  background: linear-gradient(180deg, #2D2F31, #0B0C0E);
  filter: contrast(1.6) brightness(.9);
}
.us-px:nth-child(3n){ filter: brightness(1.15) }
.us-px:nth-child(7n){ filter: brightness(.7) }

/* PET hotspot */
.pet-hotspot{
  position:absolute; left:52%; top:38%; transform:translate(-50%,-50%);
  width:64px; height:64px; border-radius:50%;
  background: radial-gradient(circle at 50% 50%, var(--pet1), var(--pet2) 60%, var(--pet3) 100%);
  box-shadow:
    0 0 20px rgba(253,224,71,.75),
    0 0 34px rgba(251,146,60,.55),
    0 0 46px rgba(239,68,68,.45);
  animation: pulse 1.8s ease-in-out infinite;
}
@keyframes pulse {
  0%,100%{ transform:translate(-50%,-50%) scale(1) }
  50%{ transform:translate(-50%,-50%) scale(1.15) }
}
.spark{
  position:absolute; width:3px; height:3px; border-radius:50%; background: #FCA311; filter: drop-shadow(0 0 6px rgba(255,196,0,.8));
  animation: drift 1.6s ease-out infinite;
}
.spark.s2{ animation-delay:.3s }
.spark.s3{ animation-delay:.6s }
@keyframes drift{
  0%{ transform: translate(0,0); opacity:1 }
  100%{ transform: translate(20px,-28px); opacity:0 }
}

/* Labels and leader lines */
.label{
  position:absolute; font-size:12px; color:var(--soft); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px);
  background: rgba(8,13,28,.55); border:1px solid rgba(255,255,255,.08); padding:6px 8px; border-radius:8px;
  box-shadow: var(--glow);
}
.line{
  position:absolute; width:2px; background: linear-gradient(180deg, rgba(46,144,250,0), rgba(46,144,250,.7));
  box-shadow: 0 0 10px rgba(46,144,250,.35);
}

/* Background data streams */
.stream{
  position:absolute; inset:0; pointer-events:none; opacity:.18;
  background-image:
    linear-gradient(to right, rgba(46,144,250,.25) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(46,144,250,.15) 1px, transparent 1px);
  background-size: 40px 40px, 40px 40px;
  animation: pan 40s linear infinite;
}
@keyframes pan {
  from{ background-position: 0 0, 0 0 }
  to{ background-position: 400px 0, 0 400px }
}

/* Small rotating hardware models */
.hardware{
  position:absolute; right:24px; bottom:24px; display:flex; gap:10px;
}
.hw{
  width:60px; height:60px; border-radius:12px; border:1px solid rgba(255,255,255,.08);
  background: rgba(255,255,255,.03);
  display:grid; place-items:center; color:#BFE4FF; font-weight:900; font-size:12px;
  box-shadow: var(--glow);
  animation: spin 10s linear infinite;
}
@keyframes spin { to { transform: rotate(360deg) } }
</style>
</head>
<body>
  <!-- Top Navigation -->
  <div class="topbar">
    <div class="brand">
      <div class="brand-badge">BI</div>
      Biomedical Imaging & Instrumentation Technology
    </div>
    <div class="nav-actions">
      <button class="nav-btn active" onclick="switchMode('learn')">Learn</button>
      <button class="nav-btn" onclick="switchMode('lab')">Lab</button>
      <button class="nav-btn" onclick="switchMode('quiz')">Quiz</button>
    </div>
  </div>

  <!-- Main Container -->
  <div class="container">
    <!-- Left Panel: Content/Theory -->
    <div class="content-panel">
      <div class="panel-header">
        <div>
          <div class="panel-title">Learning Module</div>
          <div class="panel-subtitle">SCQA Framework: Situation → Complication → Question → Answer</div>
        </div>
      </div>
      <div class="panel-content" id="content-area">
        <!-- Content will be dynamically loaded here -->
      </div>
    </div>

    <!-- Right Panel: Interactive Lab -->
    <div class="lab-panel">
      <div class="panel-header">
        <div>
          <div class="panel-title">Interactive Visualization Lab</div>
          <div class="panel-subtitle">Real-time imaging simulation and parameter control</div>
        </div>
      </div>
      <div class="lab-canvas" id="lab-canvas">
        <!-- Lab visualization will be rendered here -->
      </div>
      <div class="controls" id="lab-controls">
        <!-- Interactive controls will be dynamically generated -->
      </div>
    </div>
  </div>

  <script>
    // Application State
    let currentMode = 'learn';
    let currentSection = 'situation';
    let isScanning = false;
    let scanProgress = 0;
    let patientData = {
      heartRate: 72,
      breathingRate: 16,
      temperature: 98.6,
      bloodPressure: { systolic: 120, diastolic: 80 }
    };

    let labParameters = {
      mri: {
        fieldStrength: 1.5, te: 20, tr: 500, fov: 240, matrix: 256,
        sliceThickness: 5, gap: 1, nex: 2, bandwidth: 31.25,
        contrast: 'T1', sequence: 'SE'
      },
      ct: {
        kvp: 120, mas: 200, sliceThickness: 5, pitch: 1.0,
        rotation: 0.5, reconstruction: 'FBP', window: 400, level: 40,
        collimation: 64, dose: 15.2
      },
      ultrasound: {
        frequency: 5, power: 50, gain: 30, depth: 12,
        focus: 6, tgc: [20, 25, 30, 35, 40], mode: 'B-mode',
        doppler: false, colorGain: 50
      },
      pet: {
        dose: 10, uptakeTime: 60, reconstruction: 'osem',
        iterations: 3, subsets: 21, filter: 'gaussian',
        attenuation: true, scatter: true, randoms: true
      }
    };

    let imageQuality = {
      snr: 85, contrast: 78, resolution: 92, artifacts: 5
    };

    let realTimeData = {
      scanTime: 0,
      noiseLevel: 12,
      signalIntensity: 1000,
      motionArtifacts: 0,
      doseMeter: 0
    };

    // Content Data Structure (SCQA Framework)
    const contentData = {
      situation: {
        title: "Situation: The Challenge of Medical Diagnosis",
        icon: "S",
        content: `
          <p>Modern healthcare faces an unprecedented challenge: <span class="highlight">how do we see inside the human body without invasive procedures?</span></p>

          <p>Every day, millions of patients worldwide require accurate diagnosis of internal conditions - from brain tumors and heart disease to bone fractures and organ dysfunction. Traditional physical examination and basic tests can only reveal so much.</p>

          <ul>
            <li><strong>Complex anatomy:</strong> The human body contains intricate structures that overlap and obscure each other</li>
            <li><strong>Diverse pathologies:</strong> Different diseases require different imaging approaches</li>
            <li><strong>Patient safety:</strong> Diagnostic methods must minimize risk and discomfort</li>
            <li><strong>Clinical efficiency:</strong> Results must be obtained quickly for timely treatment</li>
          </ul>

          <p>This fundamental challenge has driven the development of sophisticated <span class="emphasis">biomedical imaging technologies</span> that can reveal internal structures and physiological processes with remarkable detail.</p>
        `
      },
      complication: {
        title: "Complication: Multiple Imaging Modalities, Each with Limitations",
        icon: "C",
        content: `
          <p>While we have developed multiple imaging technologies, <span class="highlight">each modality has specific strengths and limitations</span> that make medical imaging complex:</p>

          <div class="section">
            <h4 style="color: var(--mri); margin-bottom: 8px;">🧲 MRI (Magnetic Resonance Imaging)</h4>
            <p><strong>Strengths:</strong> Excellent soft tissue contrast, no ionizing radiation, functional imaging capabilities</p>
            <p><strong>Limitations:</strong> Long scan times, expensive, contraindicated for some implants, claustrophobia</p>
          </div>

          <div class="section">
            <h4 style="color: var(--cyan); margin-bottom: 8px;">⚡ CT (Computed Tomography)</h4>
            <p><strong>Strengths:</strong> Fast acquisition, excellent bone detail, widely available, emergency-friendly</p>
            <p><strong>Limitations:</strong> Ionizing radiation, limited soft tissue contrast, contrast agent reactions</p>
          </div>

          <div class="section">
            <h4 style="color: var(--us); margin-bottom: 8px;">🔊 Ultrasound</h4>
            <p><strong>Strengths:</strong> Real-time imaging, portable, safe, cost-effective</p>
            <p><strong>Limitations:</strong> Operator-dependent, limited by gas/bone, restricted field of view</p>
          </div>

          <div class="section">
            <h4 style="color: var(--pet1); margin-bottom: 8px;">☢️ PET (Positron Emission Tomography)</h4>
            <p><strong>Strengths:</strong> Functional/metabolic information, early disease detection, quantitative</p>
            <p><strong>Limitations:</strong> Radioactive tracers, expensive, limited resolution, specialized facilities</p>
          </div>
        `
      },
      question: {
        title: "Question: How Do These Technologies Work and When Should Each Be Used?",
        icon: "Q",
        content: `
          <p>To effectively utilize medical imaging, healthcare professionals must understand:</p>

          <div class="section">
            <h4 style="color: var(--accent);">🔬 Physical Principles</h4>
            <ul>
              <li>How does each technology generate images from different physical phenomena?</li>
              <li>What are the fundamental physics behind signal generation and detection?</li>
              <li>How do imaging parameters affect image quality and patient safety?</li>
            </ul>
          </div>

          <div class="section">
            <h4 style="color: var(--teal);">⚙️ Instrumentation & Technology</h4>
            <ul>
              <li>What are the key hardware components and their functions?</li>
              <li>How do modern digital systems process and reconstruct images?</li>
              <li>What quality control measures ensure optimal performance?</li>
            </ul>
          </div>

          <div class="section">
            <h4 style="color: var(--ok);">🎯 Clinical Applications</h4>
            <ul>
              <li>Which modality is best suited for specific clinical scenarios?</li>
              <li>How do we optimize protocols for different patient populations?</li>
              <li>What are the safety considerations and contraindications?</li>
            </ul>
          </div>

          <p><span class="emphasis">The interactive lab</span> on the right allows you to explore these concepts hands-on by adjusting imaging parameters and observing their effects in real-time.</p>
        `
      },
      answer: {
        title: "Answer: Integrated Understanding Through Interactive Learning",
        icon: "A",
        content: `
          <p>This module provides <span class="highlight">comprehensive, hands-on understanding</span> of biomedical imaging technologies through:</p>

          <div class="section">
            <h4 style="color: var(--accent);">📚 Theoretical Foundation</h4>
            <p>Deep dive into the physics, instrumentation, and clinical applications of each imaging modality. Understanding the fundamental principles enables informed decision-making in clinical practice.</p>
          </div>

          <div class="section">
            <h4 style="color: var(--teal);">🧪 Interactive Simulation</h4>
            <p>Real-time parameter adjustment and visualization help you understand the relationship between technical settings and image quality. Experiment with different scenarios safely.</p>
          </div>

          <div class="section">
            <h4 style="color: var(--ok);">🎯 Clinical Integration</h4>
            <p>Case-based learning connects theoretical knowledge to practical applications. Learn when and how to apply each technology for optimal patient care.</p>
          </div>

          <div class="section">
            <h4 style="color: var(--warn);">⚡ Key Learning Outcomes</h4>
            <ul>
              <li>Master the physical principles underlying each imaging modality</li>
              <li>Understand instrumentation components and their functions</li>
              <li>Optimize imaging parameters for different clinical scenarios</li>
              <li>Recognize safety considerations and contraindications</li>
              <li>Make informed decisions about modality selection</li>
            </ul>
          </div>

          <p><strong>Ready to explore?</strong> Use the interactive lab to experiment with imaging parameters and see their effects in real-time. Switch between modalities using the controls on the right panel.</p>
        `
      }
    };

    // Navigation Functions
    function switchMode(mode) {
      currentMode = mode;

      // Update navigation buttons
      document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      event.target.classList.add('active');

      // Update content based on mode
      if (mode === 'learn') {
        loadContent(currentSection);
        initializeLab();
      } else if (mode === 'lab') {
        loadLabMode();
      } else if (mode === 'quiz') {
        loadQuizMode();
      }
    }

    function loadContent(section) {
      const contentArea = document.getElementById('content-area');
      const data = contentData[section];

      contentArea.innerHTML = `
        <div class="section">
          <div class="section-title">
            <div class="section-icon">${data.icon}</div>
            ${data.title}
          </div>
          <div class="section-content">
            ${data.content}
          </div>
        </div>

        <div class="section-navigation" style="margin-top: 20px; display: flex; gap: 8px;">
          ${section !== 'situation' ? `<button type="button" class="btn" onclick="navigateSection('${getPreviousSection(section)}')">← Previous</button>` : ''}
          ${section !== 'answer' ? `<button type="button" class="btn primary" onclick="navigateSection('${getNextSection(section)}')">Next →</button>` : ''}
        </div>
      `;
    }

    function navigateSection(section) {
      currentSection = section;
      loadContent(section);
    }

    function getPreviousSection(current) {
      const sections = ['situation', 'complication', 'question', 'answer'];
      const index = sections.indexOf(current);
      return index > 0 ? sections[index - 1] : current;
    }

    function getNextSection(current) {
      const sections = ['situation', 'complication', 'question', 'answer'];
      const index = sections.indexOf(current);
      return index < sections.length - 1 ? sections[index + 1] : current;
    }

    // Lab Functions
    function initializeLab() {
      const canvas = document.getElementById('lab-canvas');
      const controls = document.getElementById('lab-controls');

      // Create lab visualization
      canvas.innerHTML = createLabVisualization();

      // Create interactive controls
      controls.innerHTML = createLabControls();

      // Start animations
      startLabAnimations();
    }

    function createLabVisualization() {
      return `
        <!-- Background data grid -->
        <div class="stream"></div>

        <!-- Central Holographic Figure -->
        <div class="figure-wrap">
          <div class="holo-figure"></div>
          <div class="holo-wire"></div>
          <div class="skeleton"></div>
          <div class="organ brain"></div>
          <div class="organ lungs"></div>
          <div class="organ heart"></div>
          <div class="organ liver"></div>
        </div>

        <!-- MRI around head -->
        <div class="mri-rings" id="mri-rings">
          <div class="ring r1"></div>
          <div class="ring r2"></div>
          <div class="ring r3"></div>
        </div>

        <!-- CT rotating beam and slices HUD -->
        <div class="ct-gantry" id="ct-gantry">
          <div class="ct-beam"></div>
        </div>
        <div class="hud" id="ct-hud">
          <div class="hud-head">
            <div>CT Reconstruction</div>
            <div class="ct-status">Slices ➜ 3D</div>
          </div>
          <div class="slices">
            <div class="slice"></div><div class="slice"></div><div class="slice"></div><div class="slice"></div>
            <div class="slice"></div><div class="slice"></div><div class="slice"></div><div class="slice"></div>
          </div>
        </div>

        <!-- Ultrasound probe and HUD -->
        <div class="us-probe" id="us-probe"></div>
        <div class="us-wave" id="us-wave1"></div>
        <div class="us-wave w2" id="us-wave2"></div>
        <div class="us-wave w3" id="us-wave3"></div>
        <div class="hud-us" id="us-hud">
          <div class="us-head">Ultrasound (Abdominal) — Sonogram</div>
          <div class="us-body">
            <!-- Faux grayscale sonogram pixels -->
            <div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div>
            <div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div>
            <div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div>
          </div>
        </div>

        <!-- PET hotspot + sparks -->
        <div class="pet-hotspot" id="pet-hotspot"></div>
        <div class="spark spark-1"></div>
        <div class="spark s2 spark-2"></div>
        <div class="spark s3 spark-3"></div>

        <!-- Labels and leader lines -->
        <div class="label label-mri">MRI: Magnetic Resonance</div>
        <div class="line line-mri"></div>

        <div class="label label-ct">CT: Computed Tomography</div>
        <div class="line line-ct"></div>

        <div class="label label-us">Ultrasound</div>
        <div class="line line-us"></div>

        <div class="label label-pet">PET: Positron Emission</div>
        <div class="line line-pet"></div>

        <!-- Small rotating hardware icons -->
        <div class="hardware">
          <div class="hw" title="Detector Crystal">CRY</div>
          <div class="hw" title="PMT">PMT</div>
          <div class="hw" title="US Array">US</div>
        </div>
      `;
    }

    function createLabControls() {
      return `
        <!-- Real-time Monitoring Dashboard -->
        <div class="monitor-dashboard">
          <div class="monitor-group">
            <div class="monitor-item">
              <div class="monitor-label">Patient Vitals</div>
              <div class="vital-display">
                <span class="vital-item">HR: <span id="heart-rate">${patientData.heartRate}</span> bpm</span>
                <span class="vital-item">RR: <span id="breathing-rate">${patientData.breathingRate}</span> /min</span>
                <span class="vital-item">BP: <span id="blood-pressure">${patientData.bloodPressure.systolic}/${patientData.bloodPressure.diastolic}</span></span>
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">Image Quality</div>
              <div class="quality-meters">
                <div class="meter">
                  <span>SNR</span>
                  <div class="meter-bar"><div class="meter-fill" style="width: ${imageQuality.snr}%"></div></div>
                  <span id="snr-value">${imageQuality.snr}%</span>
                </div>
                <div class="meter">
                  <span>Contrast</span>
                  <div class="meter-bar"><div class="meter-fill" style="width: ${imageQuality.contrast}%"></div></div>
                  <span id="contrast-value">${imageQuality.contrast}%</span>
                </div>
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">Scan Status</div>
              <div class="scan-display">
                <div class="scan-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" id="scan-progress" style="width: 0%"></div>
                  </div>
                  <span id="scan-time">00:00</span>
                </div>
                <button type="button" class="scan-btn" id="scan-toggle" onclick="toggleScan()">
                  <span id="scan-text">Start Scan</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modality Selector -->
        <div class="modality-selector">
          <button type="button" class="modality-btn active" data-modality="mri" onclick="switchModality('mri')">
            <div class="modality-icon">🧲</div>
            <span>MRI</span>
          </button>
          <button type="button" class="modality-btn" data-modality="ct" onclick="switchModality('ct')">
            <div class="modality-icon">⚡</div>
            <span>CT</span>
          </button>
          <button type="button" class="modality-btn" data-modality="ultrasound" onclick="switchModality('ultrasound')">
            <div class="modality-icon">🔊</div>
            <span>US</span>
          </button>
          <button type="button" class="modality-btn" data-modality="pet" onclick="switchModality('pet')">
            <div class="modality-icon">☢️</div>
            <span>PET</span>
          </button>
        </div>

        <!-- Dynamic Parameter Controls -->
        <div class="parameter-controls" id="parameter-controls">
          <!-- MRI Controls -->
          <div class="control-panel" id="mri-controls" style="display: block;">
            <div class="panel-header-small">
              <h4>MRI Acquisition Parameters</h4>
              <div class="sequence-selector">
                <select onchange="updateSequence(this.value)" class="param-select">
                  <option value="SE">Spin Echo</option>
                  <option value="GRE">Gradient Echo</option>
                  <option value="EPI">Echo Planar</option>
                  <option value="FLAIR">FLAIR</option>
                </select>
              </div>
            </div>

            <div class="param-grid">
              <div class="param-group">
                <label class="param-label">Field Strength</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="0.5" max="7" step="0.1" value="1.5"
                         onchange="updateParameter('mri', 'fieldStrength', this.value)" oninput="liveUpdate('mri', 'fieldStrength', this.value)">
                  <span class="param-value" id="mri-field">1.5T</span>
                </div>
                <div class="param-effect">Higher field = Better SNR, More artifacts</div>
              </div>

              <div class="param-group">
                <label class="param-label">TE (Echo Time)</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="5" max="100" step="1" value="20"
                         onchange="updateParameter('mri', 'te', this.value)" oninput="liveUpdate('mri', 'te', this.value)">
                  <span class="param-value" id="mri-te">20ms</span>
                </div>
                <div class="param-effect">Longer TE = More T2 weighting</div>
              </div>

              <div class="param-group">
                <label class="param-label">TR (Repetition Time)</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="100" max="3000" step="10" value="500"
                         onchange="updateParameter('mri', 'tr', this.value)" oninput="liveUpdate('mri', 'tr', this.value)">
                  <span class="param-value" id="mri-tr">500ms</span>
                </div>
                <div class="param-effect">Longer TR = More T1 weighting</div>
              </div>

              <div class="param-group">
                <label class="param-label">Matrix Size</label>
                <div class="param-control">
                  <select onchange="updateParameter('mri', 'matrix', this.value)" class="param-select">
                    <option value="128">128×128</option>
                    <option value="256" selected>256×256</option>
                    <option value="512">512×512</option>
                  </select>
                </div>
                <div class="param-effect">Higher matrix = Better resolution, Longer scan</div>
              </div>

              <div class="param-group">
                <label class="param-label">Slice Thickness</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="1" max="10" step="0.5" value="5"
                         onchange="updateParameter('mri', 'sliceThickness', this.value)" oninput="liveUpdate('mri', 'sliceThickness', this.value)">
                  <span class="param-value" id="mri-slice">5mm</span>
                </div>
                <div class="param-effect">Thinner slices = Better detail, Lower SNR</div>
              </div>

              <div class="param-group">
                <label class="param-label">NEX (Averages)</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="1" max="8" step="1" value="2"
                         onchange="updateParameter('mri', 'nex', this.value)" oninput="liveUpdate('mri', 'nex', this.value)">
                  <span class="param-value" id="mri-nex">2</span>
                </div>
                <div class="param-effect">More averages = Better SNR, Longer scan</div>
              </div>
            </div>

            <div class="real-time-feedback">
              <div class="feedback-item">
                <span>Estimated Scan Time:</span>
                <span id="mri-scan-time" class="feedback-value">2:45</span>
              </div>
              <div class="feedback-item">
                <span>SAR Level:</span>
                <span id="mri-sar" class="feedback-value">1.2 W/kg</span>
              </div>
            </div>
          </div>

          <!-- CT Controls -->
          <div class="control-panel" id="ct-controls" style="display: none;">
            <div class="panel-header-small">
              <h4>CT Acquisition Parameters</h4>
              <div class="protocol-selector">
                <select onchange="loadCTProtocol(this.value)" class="param-select">
                  <option value="head">Head</option>
                  <option value="chest">Chest</option>
                  <option value="abdomen">Abdomen</option>
                  <option value="cardiac">Cardiac</option>
                </select>
              </div>
            </div>

            <div class="param-grid">
              <div class="param-group">
                <label class="param-label">kVp (Tube Voltage)</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="80" max="140" step="1" value="120"
                         onchange="updateParameter('ct', 'kvp', this.value)" oninput="liveUpdate('ct', 'kvp', this.value)">
                  <span class="param-value" id="ct-kvp">120kVp</span>
                </div>
                <div class="param-effect">Higher kVp = Better penetration, Lower contrast</div>
              </div>

              <div class="param-group">
                <label class="param-label">mAs (Tube Current)</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="50" max="500" step="10" value="200"
                         onchange="updateParameter('ct', 'mas', this.value)" oninput="liveUpdate('ct', 'mas', this.value)">
                  <span class="param-value" id="ct-mas">200mAs</span>
                </div>
                <div class="param-effect">Higher mAs = Lower noise, Higher dose</div>
              </div>

              <div class="param-group">
                <label class="param-label">Slice Thickness</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="0.5" max="10" step="0.5" value="5"
                         onchange="updateParameter('ct', 'sliceThickness', this.value)" oninput="liveUpdate('ct', 'sliceThickness', this.value)">
                  <span class="param-value" id="ct-slice">5mm</span>
                </div>
                <div class="param-effect">Thinner slices = Better detail, More noise</div>
              </div>

              <div class="param-group">
                <label class="param-label">Pitch</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="0.5" max="2.0" step="0.1" value="1.0"
                         onchange="updateParameter('ct', 'pitch', this.value)" oninput="liveUpdate('ct', 'pitch', this.value)">
                  <span class="param-value" id="ct-pitch">1.0</span>
                </div>
                <div class="param-effect">Higher pitch = Faster scan, Lower dose</div>
              </div>
            </div>

            <div class="real-time-feedback">
              <div class="feedback-item">
                <span>Dose (CTDIvol):</span>
                <span id="ct-dose" class="feedback-value">15.2 mGy</span>
              </div>
              <div class="feedback-item">
                <span>Scan Time:</span>
                <span id="ct-scan-time" class="feedback-value">8.5s</span>
              </div>
            </div>
          </div>

          <!-- Ultrasound Controls -->
          <div class="control-panel" id="ultrasound-controls" style="display: none;">
            <div class="panel-header-small">
              <h4>Ultrasound Parameters</h4>
              <div class="mode-selector">
                <select onchange="updateUSMode(this.value)" class="param-select">
                  <option value="bmode">B-Mode</option>
                  <option value="doppler">Doppler</option>
                  <option value="color">Color Flow</option>
                </select>
              </div>
            </div>

            <div class="param-grid">
              <div class="param-group">
                <label class="param-label">Frequency</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="2" max="15" step="0.5" value="5"
                         onchange="updateParameter('ultrasound', 'frequency', this.value)" oninput="liveUpdate('ultrasound', 'frequency', this.value)">
                  <span class="param-value" id="us-freq">5MHz</span>
                </div>
                <div class="param-effect">Higher frequency = Better resolution, Less penetration</div>
              </div>

              <div class="param-group">
                <label class="param-label">Power</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="10" max="100" step="5" value="50"
                         onchange="updateParameter('ultrasound', 'power', this.value)" oninput="liveUpdate('ultrasound', 'power', this.value)">
                  <span class="param-value" id="us-power">50%</span>
                </div>
                <div class="param-effect">Higher power = Better penetration, Safety concerns</div>
              </div>

              <div class="param-group">
                <label class="param-label">Gain</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="0" max="60" step="2" value="30"
                         onchange="updateParameter('ultrasound', 'gain', this.value)" oninput="liveUpdate('ultrasound', 'gain', this.value)">
                  <span class="param-value" id="us-gain">30dB</span>
                </div>
                <div class="param-effect">Higher gain = Brighter image, More noise</div>
              </div>

              <div class="param-group">
                <label class="param-label">Depth</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="5" max="25" step="1" value="12"
                         onchange="updateParameter('ultrasound', 'depth', this.value)" oninput="liveUpdate('ultrasound', 'depth', this.value)">
                  <span class="param-value" id="us-depth">12cm</span>
                </div>
                <div class="param-effect">Greater depth = Lower frame rate</div>
              </div>
            </div>
          </div>

          <!-- PET Controls -->
          <div class="control-panel" id="pet-controls" style="display: none;">
            <div class="panel-header-small">
              <h4>PET Acquisition Parameters</h4>
              <div class="tracer-selector">
                <select onchange="updateTracer(this.value)" class="param-select">
                  <option value="fdg">FDG (Glucose)</option>
                  <option value="florbetapir">Florbetapir (Amyloid)</option>
                  <option value="flutemetamol">Flutemetamol (Amyloid)</option>
                </select>
              </div>
            </div>

            <div class="param-grid">
              <div class="param-group">
                <label class="param-label">Injected Dose</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="5" max="20" step="1" value="10"
                         onchange="updateParameter('pet', 'dose', this.value)" oninput="liveUpdate('pet', 'dose', this.value)">
                  <span class="param-value" id="pet-dose">10mCi</span>
                </div>
                <div class="param-effect">Higher dose = Better image quality, Higher radiation</div>
              </div>

              <div class="param-group">
                <label class="param-label">Uptake Time</label>
                <div class="param-control">
                  <input type="range" class="param-slider" min="30" max="120" step="5" value="60"
                         onchange="updateParameter('pet', 'uptakeTime', this.value)" oninput="liveUpdate('pet', 'uptakeTime', this.value)">
                  <span class="param-value" id="pet-uptake">60min</span>
                </div>
                <div class="param-effect">Longer uptake = Better tracer distribution</div>
              </div>

              <div class="param-group">
                <label class="param-label">Reconstruction</label>
                <div class="param-control">
                  <select onchange="updateParameter('pet', 'reconstruction', this.value)" class="param-select">
                    <option value="osem">OSEM</option>
                    <option value="fbp">FBP</option>
                    <option value="map">MAP</option>
                  </select>
                </div>
                <div class="param-effect">OSEM provides best image quality</div>
              </div>
            </div>

            <div class="real-time-feedback">
              <div class="feedback-item">
                <span>Effective Dose:</span>
                <span id="pet-effective-dose" class="feedback-value">7.0 mSv</span>
              </div>
              <div class="feedback-item">
                <span>Scan Duration:</span>
                <span id="pet-scan-duration" class="feedback-value">20 min</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div style="display: flex; gap: 8px; margin-top: 16px; padding: 16px; border-top: 1px solid rgba(255,255,255,0.06);">
          <button type="button" class="btn" onclick="resetParameters()">Reset All</button>
          <button type="button" class="btn primary" onclick="captureImage()">Capture Image</button>
          <button type="button" class="btn" onclick="exportReport()">Export Report</button>
        </div>
      `;
    }

    // Enhanced Interactive Functions
    function switchModality(modality) {
      // Update modality buttons
      document.querySelectorAll('.modality-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-modality="${modality}"]`).classList.add('active');

      // Show/hide control panels
      document.querySelectorAll('.control-panel').forEach(panel => {
        panel.style.display = 'none';
      });
      document.getElementById(`${modality}-controls`).style.display = 'block';

      // Update visualization focus
      updateModalityFocus(modality);
    }

    function updateModalityFocus(modality) {
      // Highlight the active modality in visualization
      const elements = {
        'mri': document.getElementById('mri-rings'),
        'ct': document.getElementById('ct-gantry'),
        'ultrasound': document.getElementById('us-probe'),
        'pet': document.getElementById('pet-hotspot')
      };

      // Reset all opacities
      Object.values(elements).forEach(el => {
        if (el) el.style.opacity = '0.3';
      });

      // Highlight active modality
      if (elements[modality]) {
        elements[modality].style.opacity = '1';
        elements[modality].style.filter = 'drop-shadow(0 0 20px rgba(46,144,250,0.6))';
      }
    }

    function liveUpdate(modality, parameter, value) {
      // Real-time parameter feedback without full update
      updateParameterDisplay(modality, parameter, value);
      updateImageQuality(modality, parameter, value);
      updateRealTimeFeedback(modality);
    }

    function updateParameter(modality, parameter, value) {
      labParameters[modality][parameter] = parseFloat(value);
      updateParameterDisplay(modality, parameter, value);
      updateVisualization(modality, parameter, value);
      updateImageQuality(modality, parameter, value);
      updateRealTimeFeedback(modality);

      // Simulate patient response to parameter changes
      simulatePatientResponse(modality, parameter, value);
    }

    function updateParameterDisplay(modality, parameter, value) {
      const displayMap = {
        'mri': {
          'fieldStrength': () => document.getElementById('mri-field').textContent = value + 'T',
          'te': () => document.getElementById('mri-te').textContent = value + 'ms',
          'tr': () => document.getElementById('mri-tr').textContent = value + 'ms',
          'sliceThickness': () => document.getElementById('mri-slice').textContent = value + 'mm',
          'nex': () => document.getElementById('mri-nex').textContent = value
        },
        'ct': {
          'kvp': () => document.getElementById('ct-kvp').textContent = value + 'kVp',
          'mas': () => document.getElementById('ct-mas').textContent = value + 'mAs',
          'sliceThickness': () => document.getElementById('ct-slice').textContent = value + 'mm',
          'pitch': () => document.getElementById('ct-pitch').textContent = value,
          'rotation': () => document.getElementById('ct-rotation').textContent = value + 's'
        },
        'ultrasound': {
          'frequency': () => document.getElementById('us-freq').textContent = value + 'MHz',
          'power': () => document.getElementById('us-power').textContent = value + '%',
          'gain': () => document.getElementById('us-gain').textContent = value + 'dB'
        },
        'pet': {
          'dose': () => document.getElementById('pet-dose').textContent = value + 'mCi',
          'uptakeTime': () => document.getElementById('pet-uptake').textContent = value + 'min'
        }
      };

      if (displayMap[modality] && displayMap[modality][parameter]) {
        displayMap[modality][parameter]();
      }
    }

    function updateImageQuality(modality, parameter, value) {
      // Simulate realistic image quality changes
      const params = labParameters[modality];

      if (modality === 'mri') {
        // Higher field strength improves SNR but may increase artifacts
        imageQuality.snr = Math.min(100, 60 + (params.fieldStrength * 15));
        imageQuality.artifacts = Math.max(0, (params.fieldStrength - 1.5) * 10);

        // More averages improve SNR
        imageQuality.snr += (params.nex - 1) * 8;

        // Thinner slices reduce SNR
        imageQuality.snr -= (5 - params.sliceThickness) * 3;

      } else if (modality === 'ct') {
        // Higher mAs reduces noise
        imageQuality.snr = Math.min(100, 40 + (params.mas / 10));

        // Higher kVp reduces contrast
        imageQuality.contrast = Math.max(20, 100 - (params.kvp - 80) * 0.8);

        // Thinner slices increase noise
        imageQuality.snr -= (5 - params.sliceThickness) * 5;
      }

      // Update quality meters
      updateQualityMeters();
    }

    function updateQualityMeters() {
      const snrMeter = document.querySelector('#snr-value');
      const contrastMeter = document.querySelector('#contrast-value');
      const snrFill = document.querySelector('.meter-fill');

      if (snrMeter) {
        snrMeter.textContent = Math.round(imageQuality.snr) + '%';
        snrFill.style.width = imageQuality.snr + '%';
      }
      if (contrastMeter) {
        contrastMeter.textContent = Math.round(imageQuality.contrast) + '%';
      }
    }

    function updateRealTimeFeedback(modality) {
      const params = labParameters[modality];

      if (modality === 'mri') {
        // Calculate scan time based on parameters
        const scanTime = (params.tr * params.matrix * params.nex) / 1000 / 60; // minutes
        const sar = (params.fieldStrength * params.fieldStrength * 0.8); // Simplified SAR calculation

        const scanTimeEl = document.getElementById('mri-scan-time');
        const sarEl = document.getElementById('mri-sar');

        if (scanTimeEl) scanTimeEl.textContent = `${Math.round(scanTime)}:${String(Math.round((scanTime % 1) * 60)).padStart(2, '0')}`;
        if (sarEl) sarEl.textContent = `${sar.toFixed(1)} W/kg`;

      } else if (modality === 'ct') {
        // Calculate dose and scan time
        const dose = (params.mas * params.kvp * params.kvp) / 100000; // Simplified dose calculation
        const scanTime = (params.rotation * 360) / params.pitch; // seconds
        const noise = 1000 / Math.sqrt(params.mas); // Noise index

        const doseEl = document.getElementById('ct-dose');
        const timeEl = document.getElementById('ct-scan-time');
        const noiseEl = document.getElementById('ct-noise');

        if (doseEl) doseEl.textContent = `${dose.toFixed(1)} mGy`;
        if (timeEl) timeEl.textContent = `${scanTime.toFixed(1)}s`;
        if (noiseEl) noiseEl.textContent = `${noise.toFixed(1)}`;
      }
    }

    function simulatePatientResponse(modality, parameter, value) {
      // Simulate realistic patient physiological responses
      if (modality === 'mri' && parameter === 'fieldStrength' && value > 3) {
        // High field strength may cause patient discomfort
        patientData.heartRate += Math.random() * 5;
      }

      if (modality === 'ct' && parameter === 'mas' && value > 300) {
        // High dose may cause slight stress response
        patientData.heartRate += Math.random() * 3;
      }

      // Add some natural variation
      patientData.heartRate += (Math.random() - 0.5) * 2;
      patientData.breathingRate += (Math.random() - 0.5) * 1;

      // Keep within realistic ranges
      patientData.heartRate = Math.max(60, Math.min(100, patientData.heartRate));
      patientData.breathingRate = Math.max(12, Math.min(20, patientData.breathingRate));

      updateVitalSigns();
    }

    function updateVitalSigns() {
      const hrEl = document.getElementById('heart-rate');
      const rrEl = document.getElementById('breathing-rate');
      const bpEl = document.getElementById('blood-pressure');

      if (hrEl) hrEl.textContent = Math.round(patientData.heartRate);
      if (rrEl) rrEl.textContent = Math.round(patientData.breathingRate);
      if (bpEl) bpEl.textContent = `${patientData.bloodPressure.systolic}/${patientData.bloodPressure.diastolic}`;
    }

    function toggleScan() {
      const scanBtn = document.getElementById('scan-toggle');
      const scanText = document.getElementById('scan-text');
      const progressBar = document.getElementById('scan-progress');

      if (!isScanning) {
        // Start scanning
        isScanning = true;
        scanProgress = 0;
        scanBtn.classList.add('scanning');
        scanText.textContent = 'Stop Scan';

        // Simulate scan progress
        const scanInterval = setInterval(() => {
          scanProgress += 2;
          progressBar.style.width = scanProgress + '%';

          // Update scan time display
          const timeEl = document.getElementById('scan-time');
          if (timeEl) {
            const minutes = Math.floor(scanProgress / 4);
            const seconds = Math.floor((scanProgress % 4) * 15);
            timeEl.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
          }

          // Simulate patient movement and artifacts
          if (Math.random() < 0.1) {
            realTimeData.motionArtifacts += Math.random() * 2;
            showNotification('Motion detected - please remain still', 'warning');
          }

          // Complete scan
          if (scanProgress >= 100) {
            clearInterval(scanInterval);
            completeScan();
          }
        }, 100);

        // Store interval for potential cancellation
        window.currentScanInterval = scanInterval;

      } else {
        // Stop scanning
        stopScan();
      }
    }

    function stopScan() {
      isScanning = false;
      const scanBtn = document.getElementById('scan-toggle');
      const scanText = document.getElementById('scan-text');

      scanBtn.classList.remove('scanning');
      scanText.textContent = 'Start Scan';

      if (window.currentScanInterval) {
        clearInterval(window.currentScanInterval);
      }

      showNotification('Scan stopped', 'info');
    }

    function completeScan() {
      isScanning = false;
      const scanBtn = document.getElementById('scan-toggle');
      const scanText = document.getElementById('scan-text');

      scanBtn.classList.remove('scanning');
      scanText.textContent = 'Start Scan';

      // Reset progress
      setTimeout(() => {
        document.getElementById('scan-progress').style.width = '0%';
        document.getElementById('scan-time').textContent = '00:00';
        scanProgress = 0;
      }, 2000);

      showNotification('Scan completed successfully!', 'success');

      // Generate scan report
      generateScanReport();
    }

    function generateScanReport() {
      const activeModality = document.querySelector('.modality-btn.active').dataset.modality;
      const params = labParameters[activeModality];

      const report = {
        modality: activeModality.toUpperCase(),
        timestamp: new Date().toLocaleString(),
        parameters: params,
        imageQuality: imageQuality,
        patientVitals: { ...patientData }
      };

      console.log('Scan Report Generated:', report);

      // Could save to localStorage or send to server
      localStorage.setItem('lastScanReport', JSON.stringify(report));
    }

    function updateVisualization(modality, parameter, value) {
      // Update visual elements based on parameter changes
      if (modality === 'mri') {
        const rings = document.getElementById('mri-rings');
        if (rings) {
          rings.style.opacity = Math.min(1, value / 3); // Higher field strength = more visible
        }
      } else if (modality === 'ct') {
        const beam = document.querySelector('.ct-beam');
        if (beam && parameter === 'kvp') {
          beam.style.opacity = Math.min(1, value / 140);
        }
      } else if (modality === 'ultrasound') {
        const waves = document.querySelectorAll('.us-wave');
        if (parameter === 'frequency') {
          waves.forEach((wave, index) => {
            wave.style.animationDuration = (3 - value / 5) + 's';
          });
        }
      } else if (modality === 'pet') {
        const hotspot = document.getElementById('pet-hotspot');
        if (hotspot && parameter === 'dose') {
          hotspot.style.opacity = Math.min(1, value / 15);
        }
      }
    }

    function resetParameters() {
      labParameters = {
        mri: { fieldStrength: 1.5, te: 20, tr: 500 },
        ct: { kvp: 120, mas: 200, sliceThickness: 5 },
        ultrasound: { frequency: 5, power: 50, gain: 30 },
        pet: { dose: 10, uptakeTime: 60, reconstruction: 'osem' }
      };

      // Reset all sliders and displays
      document.querySelectorAll('.slider').forEach(slider => {
        const defaultValues = {
          'mri-field': 1.5, 'mri-te': 20, 'mri-tr': 500,
          'ct-kvp': 120, 'ct-mas': 200, 'ct-slice': 5,
          'us-freq': 5, 'us-power': 50, 'us-gain': 30,
          'pet-dose': 10, 'pet-uptake': 60
        };

        // Find corresponding default value and reset
        Object.keys(defaultValues).forEach(key => {
          if (slider.onchange && slider.onchange.toString().includes(key.split('-')[1])) {
            slider.value = defaultValues[key];
            slider.onchange();
          }
        });
      });

      // Reset visualizations
      initializeLab();
    }

    function captureImage() {
      // Simulate image capture
      const canvas = document.getElementById('lab-canvas');
      const flash = document.createElement('div');
      flash.style.cssText = `
        position: absolute; inset: 0; background: rgba(255,255,255,0.8);
        pointer-events: none; z-index: 1000; opacity: 0;
        transition: opacity 0.1s ease;
      `;
      canvas.appendChild(flash);

      // Flash effect
      setTimeout(() => flash.style.opacity = '1', 10);
      setTimeout(() => flash.style.opacity = '0', 150);
      setTimeout(() => canvas.removeChild(flash), 250);

      // Show capture notification
      showNotification('Image captured successfully!', 'success');
    }

    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed; top: 80px; right: 20px; z-index: 1000;
        padding: 12px 16px; border-radius: 8px; font-size: 12px; font-weight: 600;
        background: ${type === 'success' ? 'var(--ok)' : 'var(--accent)'};
        color: white; box-shadow: var(--shadow);
        transform: translateX(100%); transition: transform 0.3s ease;
      `;
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => notification.style.transform = 'translateX(0)', 10);
      setTimeout(() => notification.style.transform = 'translateX(100%)', 3000);
      setTimeout(() => document.body.removeChild(notification), 3300);
    }

    function startLabAnimations() {
      // Add CSS animations for lab elements
      const style = document.createElement('style');
      style.textContent = `
        .ct-status { color: var(--cyan); font-weight: 800; }
        .label-mri { left: 50%; top: 10%; transform: translate(-50%,0); border-color: rgba(186,230,253,.25); }
        .line-mri { left: 50%; top: 12.5%; height: 50px; transform: translateX(-1px); }
        .label-ct { right: 3%; top: 8%; }
        .line-ct { right: 10%; top: 13%; height: 110px; background: linear-gradient(180deg, rgba(103,232,249,.0), rgba(103,232,249,.8)); }
        .label-us { left: 4%; bottom: 22%; }
        .line-us { left: 7%; bottom: 22%; height: 90px; background: linear-gradient(180deg, rgba(134,239,172,.0), rgba(134,239,172,.8)); }
        .label-pet { left: 55%; top: 30%; background: rgba(40,16,0,.45); border-color: rgba(255,196,0,.25); color: #FFE08A; }
        .line-pet { left: 57%; top: 33%; height: 60px; background: linear-gradient(180deg, rgba(253,224,71,.0), rgba(239,68,68,.85)); }
        .spark-1 { left: 52%; top: 38%; }
        .spark-2 { left: 52%; top: 38%; }
        .spark-3 { left: 52%; top: 38%; }
      `;
      document.head.appendChild(style);
    }

    function loadLabMode() {
      const contentArea = document.getElementById('content-area');
      contentArea.innerHTML = `
        <div class="section">
          <div class="section-title">
            <div class="section-icon">🧪</div>
            Interactive Laboratory Mode
          </div>
          <div class="section-content">
            <p>Welcome to the <span class="highlight">Interactive Imaging Laboratory</span>! This mode allows you to:</p>
            <ul>
              <li><strong>Adjust Parameters:</strong> Use the controls on the right to modify imaging parameters in real-time</li>
              <li><strong>Observe Effects:</strong> Watch how parameter changes affect image quality and acquisition</li>
              <li><strong>Compare Modalities:</strong> Switch between different imaging techniques</li>
              <li><strong>Capture Images:</strong> Save parameter configurations for later analysis</li>
            </ul>
            <p><span class="emphasis">Try adjusting the sliders</span> to see how different parameters affect each imaging modality!</p>
          </div>
        </div>
      `;
    }

    function loadQuizMode() {
      const contentArea = document.getElementById('content-area');
      contentArea.innerHTML = `
        <div class="section">
          <div class="section-title">
            <div class="section-icon">📝</div>
            Knowledge Assessment
          </div>
          <div class="section-content">
            <p>Test your understanding of biomedical imaging principles:</p>
            <div style="margin: 16px 0; padding: 12px; background: rgba(255,255,255,0.02); border-radius: 8px;">
              <p><strong>Question 1:</strong> Which imaging modality provides the best soft tissue contrast without ionizing radiation?</p>
              <div style="margin: 8px 0;">
                <label style="display: block; margin: 4px 0; cursor: pointer;">
                  <input type="radio" name="q1" value="mri" style="margin-right: 8px;"> MRI (Magnetic Resonance Imaging)
                </label>
                <label style="display: block; margin: 4px 0; cursor: pointer;">
                  <input type="radio" name="q1" value="ct" style="margin-right: 8px;"> CT (Computed Tomography)
                </label>
                <label style="display: block; margin: 4px 0; cursor: pointer;">
                  <input type="radio" name="q1" value="us" style="margin-right: 8px;"> Ultrasound
                </label>
                <label style="display: block; margin: 4px 0; cursor: pointer;">
                  <input type="radio" name="q1" value="pet" style="margin-right: 8px;"> PET (Positron Emission Tomography)
                </label>
              </div>
            </div>
            <button type="button" class="btn primary" onclick="checkAnswer()">Check Answer</button>
          </div>
        </div>
      `;
    }

    function checkAnswer() {
      const selected = document.querySelector('input[name="q1"]:checked');
      if (selected) {
        if (selected.value === 'mri') {
          showNotification('Correct! MRI provides excellent soft tissue contrast without ionizing radiation.', 'success');
        } else {
          showNotification('Incorrect. MRI is the correct answer - it provides the best soft tissue contrast without radiation.', 'info');
        }
      } else {
        showNotification('Please select an answer first.', 'info');
      }
    }

    // Additional Interactive Functions
    function loadCTProtocol(protocol) {
      const protocols = {
        head: { kvp: 120, mas: 300, sliceThickness: 5, pitch: 1.0 },
        chest: { kvp: 120, mas: 150, sliceThickness: 1.25, pitch: 1.5 },
        abdomen: { kvp: 120, mas: 200, sliceThickness: 2.5, pitch: 1.2 },
        cardiac: { kvp: 100, mas: 400, sliceThickness: 0.6, pitch: 0.2 }
      };

      const params = protocols[protocol];
      if (params) {
        Object.keys(params).forEach(key => {
          updateParameter('ct', key, params[key]);
          // Update slider values
          const slider = document.querySelector(`input[onchange*="${key}"]`);
          if (slider) slider.value = params[key];
        });
        showNotification(`${protocol.charAt(0).toUpperCase() + protocol.slice(1)} protocol loaded`, 'success');
      }
    }

    function updateSequence(sequence) {
      const sequences = {
        SE: { te: 20, tr: 500 },
        GRE: { te: 5, tr: 25 },
        EPI: { te: 30, tr: 3000 },
        FLAIR: { te: 120, tr: 9000 }
      };

      const params = sequences[sequence];
      if (params) {
        Object.keys(params).forEach(key => {
          updateParameter('mri', key, params[key]);
        });
      }
    }

    function updateWindowLevel(preset) {
      const presets = {
        soft: { window: 400, level: 40 },
        lung: { window: 1500, level: -600 },
        bone: { window: 2000, level: 300 },
        brain: { window: 80, level: 40 }
      };

      const params = presets[preset];
      if (params) {
        labParameters.ct.window = params.window;
        labParameters.ct.level = params.level;
        updateVisualization('ct', 'window', params.window);
      }
    }

    function updateUSMode(mode) {
      labParameters.ultrasound.mode = mode;
      const probe = document.getElementById('us-probe');
      const waves = document.querySelectorAll('.us-wave');

      if (mode === 'doppler') {
        if (probe) probe.style.filter = 'hue-rotate(60deg)';
        waves.forEach(wave => wave.style.borderColor = 'rgba(255, 100, 100, 0.7)');
      } else if (mode === 'color') {
        if (probe) probe.style.filter = 'hue-rotate(120deg)';
        waves.forEach(wave => wave.style.borderColor = 'rgba(100, 255, 100, 0.7)');
      } else {
        if (probe) probe.style.filter = 'none';
        waves.forEach(wave => wave.style.borderColor = 'rgba(134,239,172,.55)');
      }
    }

    function updateTracer(tracer) {
      labParameters.pet.tracer = tracer;
      const hotspot = document.getElementById('pet-hotspot');

      const tracerColors = {
        fdg: 'radial-gradient(circle, #FDE047, #FB923C 60%, #EF4444)',
        florbetapir: 'radial-gradient(circle, #A78BFA, #8B5CF6 60%, #7C3AED)',
        flutemetamol: 'radial-gradient(circle, #34D399, #10B981 60%, #059669)'
      };

      if (hotspot && tracerColors[tracer]) {
        hotspot.style.background = tracerColors[tracer];
      }
    }

    function exportReport() {
      const report = JSON.parse(localStorage.getItem('lastScanReport') || '{}');
      if (Object.keys(report).length === 0) {
        showNotification('No scan data available. Please complete a scan first.', 'warning');
        return;
      }

      const reportText = `
IMAGING REPORT
==============
Modality: ${report.modality}
Date/Time: ${report.timestamp}

ACQUISITION PARAMETERS:
${Object.entries(report.parameters).map(([key, value]) => `${key}: ${value}`).join('\n')}

IMAGE QUALITY METRICS:
SNR: ${report.imageQuality.snr}%
Contrast: ${report.imageQuality.contrast}%
Resolution: ${report.imageQuality.resolution}%
Artifacts: ${report.imageQuality.artifacts}%

PATIENT VITALS:
Heart Rate: ${report.patientVitals.heartRate} bpm
Breathing Rate: ${report.patientVitals.breathingRate} /min
Blood Pressure: ${report.patientVitals.bloodPressure.systolic}/${report.patientVitals.bloodPressure.diastolic} mmHg
      `;

      // Create and download file
      const blob = new Blob([reportText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `imaging_report_${Date.now()}.txt`;
      a.click();
      URL.revokeObjectURL(url);

      showNotification('Report exported successfully!', 'success');
    }

    // Real-time monitoring loop
    function startRealTimeMonitoring() {
      setInterval(() => {
        // Simulate natural vital sign variations
        patientData.heartRate += (Math.random() - 0.5) * 0.5;
        patientData.breathingRate += (Math.random() - 0.5) * 0.2;

        // Keep within normal ranges
        patientData.heartRate = Math.max(65, Math.min(85, patientData.heartRate));
        patientData.breathingRate = Math.max(14, Math.min(18, patientData.breathingRate));

        updateVitalSigns();

        // Update noise levels
        realTimeData.noiseLevel += (Math.random() - 0.5) * 0.5;
        realTimeData.noiseLevel = Math.max(8, Math.min(15, realTimeData.noiseLevel));

      }, 2000); // Update every 2 seconds
    }

    // Initialize the application
    document.addEventListener('DOMContentLoaded', function() {
      loadContent('situation');
      initializeLab();
      startRealTimeMonitoring();

      // Initialize with MRI as default
      switchModality('mri');

      // Update initial displays
      updateVitalSigns();
      updateQualityMeters();
    });
  </script>
</body>
</html>
