<!doctype html>
<html lang="en" dir="ltr">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="description" content="BioMed LMS – Advancing Biomedical Engineering Education. Interactive virtual labs for imaging, medical electronics, biomechanics, and rehabilitation engineering." />
  <meta name="keywords" content="Biomedical Engineering, BioMed LMS, Medical Electronics, Biomedical Instrumentation, Imaging, Biomechanics, Rehabilitation, Virtual Lab, ECG, EMG, EEG, MRI, CT, Ultrasound, PET" />
  <meta name="author" content="Dr<PERSON> <PERSON>, SUST - BME" />
  <link rel="icon" href="https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/svg/1f9ec.svg" />
  <title>BioMed LMS – Advancing Biomedical Engineering Education</title>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet" />

  <!-- Font Awesome Icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" integrity="sha512-Dx1Xyq1Xv2j3s8e1O3x2n0bq0mR3JdY0yq3m6G7V4b9i7mAqf1mXbH1m9uK2QyQJ1Gm7sH2V8m0y7qJcC8+9Ww==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <style>
    :root{
      /* Dark Mode spec */
      --bg:#101828;           /* deep charcoal/navy */
      --card:#111b2a;
      --muted:#9AA6B2;
      --primary:#2E90FA;      /* teal/blue accent */
      --accent:#5EEAD4;       /* cool teal for subtle accents */
      --text:#E6EEF8;
      --ring: 0 0 0 3px rgba(46,144,250,.35);
      --radius:16px;
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{
      margin:0;
      font-family: 'Poppins', 'Roboto', system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
      background: radial-gradient(1200px 600px at 20% 0%, #0c1424 0%, var(--bg) 45%) fixed;
      color:var(--text);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    a{color:inherit;text-decoration:none}
    img{max-width:100%;display:block}
    .container{width:min(1100px, 92%);margin-inline:auto}

    /* Top bar */
    .topbar{
      display:flex;align-items:center;justify-content:space-between;
      padding:14px 0;
    }
    .brand{
      display:flex;align-items:center;gap:12px;font-weight:700;letter-spacing:.3px
    }
    .brand .logo{
      width:40px;height:40px;border-radius:12px;
      background: radial-gradient(75% 75% at 25% 25%, var(--accent) 0%, var(--primary) 70%, #0b1220 100%);
      box-shadow: 0 8px 40px rgba(0,180,216,.25), inset 0 0 16px rgba(128,255,219,.35);
    }
    .lang-toggle{
      display:flex;gap:8px;background:rgba(255,255,255,.04);border:1px solid rgba(255,255,255,.08);
      padding:6px;border-radius:999px;
    }
    .lang-toggle button{
      background:transparent;border:0;color:var(--muted);
      padding:8px 14px;border-radius:999px;cursor:pointer;font-weight:600
    }
    .lang-toggle button.active{background:var(--primary);color:#031017;box-shadow: var(--ring)}

    /* Hero */
    .hero{
      display:grid;grid-template-columns:1.1fr .9fr;gap:32px;align-items:center;
      padding:28px 0 18px;
    }
    .badge{
      display:inline-flex;align-items:center;gap:8px;
      background: rgba(0,180,216,.12);
      color: var(--accent);
      border:1px solid rgba(128,255,219,.35);
      padding:6px 12px;border-radius:999px;font-weight:600;font-size:12px;letter-spacing:.4px;
    }
    h1{
      margin:12px 0 10px;font-size:clamp(28px, 3.3vw, 44px);line-height:1.15;
      background: linear-gradient(180deg, #ffffff, #b8d8ff 70%, #80ffdb);
      -webkit-background-clip:text;background-clip:text;color:transparent;
    }
    .sub{
      color:var(--muted);font-weight:500;line-height:1.6;margin:0 0 18px;font-size:15px
    }
    .cta{
      display:flex;gap:12px;flex-wrap:wrap
    }
    .btn{
      padding:12px 18px;border-radius:12px;font-weight:700;border:0;cursor:pointer;
      transition:transform .15s ease, box-shadow .2s ease, background .2s ease; display:inline-flex; align-items:center; gap:8px;
    }
    .btn-primary{background:var(--primary);color:#061018;box-shadow:0 8px 30px rgba(46,144,250,.35)}
    .btn-primary:hover{transform:translateY(-1px);box-shadow:0 10px 36px rgba(0,180,216,.36)}
    .btn-ghost{background:rgba(255,255,255,.05);color:#cfe7ff;border:1px solid rgba(255,255,255,.12)}
    .btn-ghost:hover{background:rgba(255,255,255,.08)}

    .hero-visual{
      position:relative;background:linear-gradient(180deg,#131a31,#0e1326);border:1px solid rgba(255,255,255,.06);
      border-radius:var(--radius);padding:18px;overflow:hidden;
      box-shadow: inset 0 0 60px rgba(0,180,216,.06), 0 10px 40px rgba(0,0,0,.45);
      min-height:230px;
    }
    .grid{
      display:grid;grid-template-columns:repeat(3,minmax(0,1fr));gap:14px
    }
    .mini-card{
      background:rgba(255,255,255,.03);border:1px solid rgba(255,255,255,.08);
      border-radius:12px;padding:14px;min-height:90px;
      transition: transform .18s ease, border-color .18s ease, background .18s ease;
    }
    .mini-card:hover{transform:translateY(-2px);border-color:rgba(46,144,250,.45);background:rgba(46,144,250,.08)}
    .mini-title{font-weight:700;font-size:13px;margin:0 0 6px;color:#eaf7ff}
    .mini-sub{margin:0;color:#9bb2c8;font-size:12px;line-height:1.5}

    /* Sections */
    .sections{padding:22px 0 12px}
    .sec{background:rgba(0,0,0,.22);border:1px solid rgba(255,255,255,.06);border-radius:var(--radius);padding:18px;margin-bottom:18px;box-shadow:0 8px 28px rgba(0,0,0,.35)}
    .sec h2{margin:2px 0 12px;font-size:20px;color:#EAF2FF;letter-spacing:.2px}
    .sec .desc{color:var(--muted);margin:0 0 14px;font-size:14px}
    .module-grid{display:grid;grid-template-columns:repeat(3,minmax(0,1fr));gap:14px}
    .module-card{
      background:linear-gradient(180deg, #0e1726, var(--card));
      border:1px solid rgba(255,255,255,.08);
      border-radius:14px;padding:14px;
      transition:transform .18s ease, box-shadow .18s ease, border-color .18s ease, background .18s ease;
    }
    .module-card:hover{
      transform:translateY(-2px);
      box-shadow:0 14px 34px rgba(0,0,0,.45), 0 0 0 1px rgba(46,144,250,.25) inset;
      border-color:rgba(46,144,250,.45);
      background:linear-gradient(180deg, #0f1a2d, #122036);
    }
    .module-card h3{margin:0 0 6px;font-size:16px;color:#EAF2FF}
    .module-card p{margin:0 0 8px;color:#C7D6E8;font-size:13px;line-height:1.6}
    .module-card ul{margin:0;padding-left:16px;color:#AFC2D6;font-size:13px}
    .module-card li{margin:4px 0}

    .cta-card{
      display:flex;align-items:center;justify-content:space-between;gap:12px;
      background:linear-gradient(90deg, rgba(46,144,250,.18), rgba(94,234,212,.12));
      border:1px solid rgba(46,144,250,.35);border-radius:14px;padding:14px;margin-top:14px
    }
    .cta-card p{margin:0;color:#e5fbff;font-weight:600}
    .cta-card .btn{white-space:nowrap}

    /* Footer */
    footer{
      margin-top:26px;border-top:1px solid rgba(255,255,255,.08);padding:14px 0;color:#a8bfd4;font-size:13px
    }
    .foot{
      display:flex;flex-wrap:wrap;align-items:center;justify-content:space-between;gap:12px
    }
    .social{display:flex;gap:10px}
    .social a{
      width:34px;height:34px;border-radius:999px;display:grid;place-items:center;
      background:rgba(255,255,255,.05);border:1px solid rgba(255,255,255,.1);color:#e8f8ff
    }
    .social a:hover{background:rgba(128,255,219,.15);border-color:rgba(128,255,219,.45);box-shadow: var(--ring)}

    /* Responsive */
    @media (max-width: 940px){
      .hero{grid-template-columns:1fr}
      .grid{grid-template-columns:repeat(2,minmax(0,1fr))}
      .module-grid{grid-template-columns:repeat(2,minmax(0,1fr))}
    }
    @media (max-width: 620px){
      .grid{grid-template-columns:1fr}
      .module-grid{grid-template-columns:1fr}
      .cta{flex-direction:column;align-items:stretch}
    }

    /* Simple appear animation */
    .fade-in{opacity:0;transform:translateY(6px);animation:fade .5s ease forwards}
    .fade-in.delay-1{animation-delay:.06s}
    .fade-in.delay-2{animation-delay:.12s}
    .fade-in.delay-3{animation-delay:.18s}
    @keyframes fade{to{opacity:1;transform:none}}
  </style>
</head>
<body>
  <header class="container topbar">
    <div class="brand">
      <div class="logo" aria-hidden="true"></div>
      <div>
        <div>BioMed LMS</div>
        <small style="color:#86c6ff;font-weight:600;">Advancing Biomedical Engineering</small>
      </div>
    </div>
    <nav class="lang-toggle" aria-label="Language toggle">
      <button id="btn-en" class="active" aria-pressed="true">EN</button>
      <button id="btn-ar" aria-pressed="false">AR</button>
    </nav>
  </header>

  <main class="container">
    <!-- Hero -->
    <section class="hero fade-in">
      <div>
        <span class="badge">
          <i class="fa-solid fa-microscope"></i>
          Virtual Labs & LMS
        </span>
        <h1 data-i18n="title">BioMed LMS – Advancing Biomedical Engineering Education</h1>
        <p class="sub" data-i18n="subtitle">
          An elite platform empowering biomedical engineering students and professionals through focused, innovative, and industry-ready learning pathways.
        </p>
        <div class="cta">
          <a class="btn btn-primary" href="#divisions"><i class="fa-solid fa-rocket"></i><span data-i18n="getStarted">Get Started</span></a>
          <a class="btn btn-ghost" href="mailto:<EMAIL>"><i class="fa-solid fa-envelope"></i><span data-i18n="contactUs">Contact Us</span></a>
        </div>
        <div style="display:flex;gap:10px;flex-wrap:wrap;margin-top:12px">
          <a class="btn btn-ghost" href="./modules/module-1/index.html" title="Principles of Medical Imaging">Module 1</a>
          <a class="btn btn-ghost" href="./modules/module-1/present.html#mri" title="Module 1 Presentation">Module 1 · Presentation</a>
          <a class="btn btn-ghost" href="./modules/module-2/index.html" title="Imaging Detectors & Hardware">Module 2</a>
          <a class="btn btn-ghost" href="./modules/module-2/present.html" title="Module 2 Presentation">Module 2 · Presentation</a>
          <a class="btn btn-ghost" href="./mindmap/index.html" title="Curriculum Napkin Mind Map">Curriculum Mind Map</a>
          <a class="btn btn-ghost" href="./modules/biomedical-measurement-mindmaps.html" title="Biomedical Measurement Mind Maps">Measurement Mind Maps</a>
        </div>
        <div style="margin-top:12px;color:#8fb5ff;font-size:12px;">
          Dr.Mohammed Yagoub Esmail, SUST - BME, © 2025 — Copy right "<EMAIL>", Phone: +249912867327, +966538076790
        </div>
      </div>
      <div class="hero-visual fade-in delay-2" aria-label="Discipline highlights">
        <div class="grid">
          <div class="mini-card">
            <div class="mini-title"><i class="fa-solid fa-x-ray"></i> <span data-i18n="imagingShort">Imaging</span></div>
            <p class="mini-sub" data-i18n="imagingBlurb">MRI, CT, PET, Ultrasound systems & instrumentation.</p>
          </div>
          <div class="mini-card">
            <div class="mini-title"><i class="fa-solid fa-heart-pulse"></i> <span data-i18n="electronicsShort">Medical Electronics</span></div>
            <p class="mini-sub" data-i18n="electronicsBlurb">ECG, EMG, EEG, sensors & data acquisition.</p>
          </div>
          <div class="mini-card">
            <div class="mini-title"><i class="fa-solid fa-person-running"></i> <span data-i18n="biomechShort">Biomechanics</span></div>
            <p class="mini-sub" data-i18n="biomechBlurb">Rehabilitation, prosthetics & motion analysis.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Three Core Divisions -->
    <section id="divisions" class="sections">
      <!-- 1. Imaging -->
      <div class="sec fade-in delay-1">
        <h2><i class="fa-solid fa-x-ray"></i> <span data-i18n="div1Title">Biomedical Imaging & Instrumentation Technology</span></h2>
        <p class="desc" data-i18n="div1Desc">Overview of MRI, CT, Ultrasound, PET imaging systems and instrumentation technologies.</p>

        <div class="module-grid">
          <article class="module-card">
            <h3 data-i18n="img101Title">Principles of Medical Imaging</h3>
            <p data-i18n="img101Desc">Covers fundamental principles of MRI, CT, PET, and ultrasound imaging modalities.</p>
            <ul>
              <li data-i18n="img101T1">MRI Physics</li>
              <li data-i18n="img101T2">CT Reconstruction</li>
              <li data-i18n="img101T3">Ultrasound Signal Processing</li>
              <li data-i18n="img101T4">Image Contrast Agents</li>
            </ul>
            <div class="cta-card" style="margin-top:10px">
              <p>Open Module 1</p>
              <a class="btn btn-primary" href="./modules/module-1/index.html">Launch</a>
              <a class="btn btn-ghost" href="./modules/module-1/present.html#mri">Presentation</a>
            </div>
          </article>
          <article class="module-card">
            <h3 data-i18n="img102Title">Imaging Detectors & Hardware</h3>
            <p data-i18n="img102Desc">Explore detector design, collimation, transducers, and analog front-ends.</p>
            <ul>
              <li data-i18n="img102T1">Scintillators & PMTs</li>
              <li data-i18n="img102T2">Ultrasound Transducers</li>
              <li data-i18n="img102T3">A/D and Timing</li>
              <li data-i18n="img102T4">Noise & SNR</li>
            </ul>
            <div class="cta-card" style="margin-top:10px">
              <p>Open Module 2</p>
              <a class="btn btn-primary" href="./modules/module-2/index.html">Launch</a>
              <a class="btn btn-ghost" href="./modules/module-2/present.html">Presentation</a>
            </div>
          </article>
          <article class="module-card">
            <h3 data-i18n="img103Title">Image Processing & Reconstruction</h3>
            <p data-i18n="img103Desc">From filtering and segmentation to iterative reconstruction methods.</p>
            <ul>
              <li data-i18n="img103T1">Fourier & Wavelets</li>
              <li data-i18n="img103T2">Iterative CT/MRI</li>
              <li data-i18n="img103T3">Denoising</li>
              <li data-i18n="img103T4">Artifacts</li>
            </ul>
          </article>
        </div>
      </div>

      <!-- 2. Medical Electronics -->
      <div class="sec fade-in delay-2">
        <h2><i class="fa-solid fa-heart-pulse"></i> <span data-i18n="div2Title">Medical Electronics & Physiological Instrumentation Measurements</span></h2>
        <p class="desc" data-i18n="div2Desc">Covers ECG, EMG, EEG, biomedical sensors, data acquisition systems.</p>

        <div class="module-grid">
          <article class="module-card">
            <h3 data-i18n="elec201Title">Biomedical Sensors & Signal Acquisition</h3>
            <p data-i18n="elec201Desc">Introduces sensors for physiological signal monitoring and methods of analog-to-digital conversion.</p>
            <ul>
              <li data-i18n="elec201T1">ECG Electrodes</li>
              <li data-i18n="elec201T2">Sensor Calibration</li>
              <li data-i18n="elec201T3">DAQ Systems</li>
              <li data-i18n="elec201T4">Signal Filtering</li>
            </ul>
          </article>
          <article class="module-card">
            <h3 data-i18n="elec202Title">Analog Front-End Design</h3>
            <p data-i18n="elec202Desc">Design low-noise amplifiers, instrumentation amplifiers, and anti-aliasing filters.</p>
            <ul>
              <li data-i18n="elec202T1">INA Topologies</li>
              <li data-i18n="elec202T2">CMRR & Noise</li>
              <li data-i18n="elec202T3">Protection & Isolation</li>
              <li data-i18n="elec202T4">Safety Standards</li>
            </ul>
          </article>
          <article class="module-card">
            <h3 data-i18n="elec203Title">Physiological Signal Processing</h3>
            <p data-i18n="elec203Desc">Time-frequency analysis and feature extraction for ECG/EMG/EEG.</p>
            <ul>
              <li data-i18n="elec203T1">Filtering & PSD</li>
              <li data-i18n="elec203T2">R-peak Detection</li>
              <li data-i18n="elec203T3">EMG Envelopes</li>
              <li data-i18n="elec203T4">EEG Bands</li>
            </ul>
          </article>
        </div>
      </div>

      <!-- 3. Biomechanics -->
      <div class="sec fade-in delay-3">
        <h2><i class="fa-solid fa-person-running"></i> <span data-i18n="div3Title">Biomechanics & Rehabilitation Engineering</span></h2>
        <p class="desc" data-i18n="div3Desc">Prosthetics, orthotics, rehabilitation devices, human motion analysis.</p>

        <div class="module-grid">
          <article class="module-card">
            <h3 data-i18n="reh301Title">Human Motion Analysis</h3>
            <p data-i18n="reh301Desc">Explores biomechanics of movement and technologies for capturing and analyzing motion.</p>
            <ul>
              <li data-i18n="reh301T1">Kinematic Modeling</li>
              <li data-i18n="reh301T2">Force Platforms</li>
              <li data-i18n="reh301T3">Gait Cycle Analysis</li>
              <li data-i18n="reh301T4">Wearables</li>
            </ul>
          </article>
          <article class="module-card">
            <h3 data-i18n="reh302Title">Prosthetics & Orthotics</h3>
            <p data-i18n="reh302Desc">Design and evaluation of assistive devices for mobility and function.</p>
            <ul>
              <li data-i18n="reh302T1">Socket Design</li>
              <li data-i18n="reh302T2">Actuation & Control</li>
              <li data-i18n="reh302T3">Material Selection</li>
              <li data-i18n="reh302T4">Clinical Metrics</li>
            </ul>
          </article>
          <article class="module-card">
            <h3 data-i18n="reh303Title">Rehabilitation Technologies</h3>
            <p data-i18n="reh303Desc">Robotics, VR-based therapy, and outcome measures.</p>
            <ul>
              <li data-i18n="reh303T1">Exoskeletons</li>
              <li data-i18n="reh303T2">FES Systems</li>
              <li data-i18n="reh303T3">VR & AR</li>
              <li data-i18n="reh303T4">Clinical Protocols</li>
            </ul>
          </article>
        </div>

        <div class="cta-card">
          <p data-i18n="ctaExplore">Learn by doing: explore interactive, virtual lab courses and simulations.</p>
          <a class="btn btn-primary" href="#divisions"><i class="fa-solid fa-circle-play"></i><span data-i18n="exploreCourses">Explore Courses</span></a>
          <a class="btn btn-ghost" href="./modules/biomedical-measurement-mindmaps.html" title="Biomedical Measurement Mind Maps">Measurement Mind Maps</a>
          <a class="btn btn-ghost" href="./mindmap/index.html" title="Curriculum Mind Map">Curriculum Mind Map</a>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <div class="container foot">
      <div>
        <div>© 2025 BioMed LMS. All Rights Reserved.</div>
        <div style="margin-top:6px">
          <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>
      </div>
      <div class="social" aria-label="Social media">
        <a href="#" title="Twitter / X" aria-label="Twitter"><i class="fa-brands fa-x-twitter"></i></a>
        <a href="#" title="LinkedIn" aria-label="LinkedIn"><i class="fa-brands fa-linkedin-in"></i></a>
        <a href="#" title="YouTube" aria-label="YouTube"><i class="fa-brands fa-youtube"></i></a>
      </div>
    </div>
  </footer>

  <!-- Minimal interactive JS: language toggle + example dynamic reveal -->
  <script>
    // Simple bilingual dictionary (EN/AR). Default EN.
    const i18n = {
      en: {
        title: "BioMed LMS – Advancing Biomedical Engineering Education",
        subtitle: "An elite platform empowering biomedical engineering students and professionals through focused, innovative, and industry-ready learning pathways.",
        getStarted: "Get Started",
        contactUs: "Contact Us",
        imagingShort: "Imaging",
        imagingBlurb: "MRI, CT, PET, Ultrasound systems & instrumentation.",
        electronicsShort: "Medical Electronics",
        electronicsBlurb: "ECG, EMG, EEG, sensors & data acquisition.",
        biomechShort: "Biomechanics",
        biomechBlurb: "Rehabilitation, prosthetics & motion analysis.",
        div1Title: "Biomedical Imaging & Instrumentation Technology",
        div1Desc: "Overview of MRI, CT, Ultrasound, PET imaging systems and instrumentation technologies.",
        img101Title: "Principles of Medical Imaging",
        img101Desc: "Covers fundamental principles of MRI, CT, PET, and ultrasound imaging modalities.",
        img101T1: "MRI Physics",
        img101T2: "CT Reconstruction",
        img101T3: "Ultrasound Signal Processing",
        img101T4: "Image Contrast Agents",
        img102Title: "Imaging Detectors & Hardware",
        img102Desc: "Explore detector design, collimation, transducers, and analog front-ends.",
        img102T1: "Scintillators & PMTs",
        img102T2: "Ultrasound Transducers",
        img102T3: "A/D and Timing",
        img102T4: "Noise & SNR",
        img103Title: "Image Processing & Reconstruction",
        img103Desc: "From filtering and segmentation to iterative reconstruction methods.",
        img103T1: "Fourier & Wavelets",
        img103T2: "Iterative CT/MRI",
        img103T3: "Denoising",
        img103T4: "Artifacts",
        div2Title: "Medical Electronics & Physiological Instrumentation Measurements",
        div2Desc: "Covers ECG, EMG, EEG, biomedical sensors, data acquisition systems.",
        elec201Title: "Biomedical Sensors & Signal Acquisition",
        elec201Desc: "Introduces sensors for physiological signal monitoring and methods of analog-to-digital conversion.",
        elec201T1: "ECG Electrodes",
        elec201T2: "Sensor Calibration",
        elec201T3: "DAQ Systems",
        elec201T4: "Signal Filtering",
        elec202Title: "Analog Front-End Design",
        elec202Desc: "Design low-noise amplifiers, instrumentation amplifiers, and anti-aliasing filters.",
        elec202T1: "INA Topologies",
        elec202T2: "CMRR & Noise",
        elec202T3: "Protection & Isolation",
        elec202T4: "Safety Standards",
        elec203Title: "Physiological Signal Processing",
        elec203Desc: "Time-frequency analysis and feature extraction for ECG/EMG/EEG.",
        elec203T1: "Filtering & PSD",
        elec203T2: "R-peak Detection",
        elec203T3: "EMG Envelopes",
        elec203T4: "EEG Bands",
        div3Title: "Biomechanics & Rehabilitation Engineering",
        div3Desc: "Prosthetics, orthotics, rehabilitation devices, human motion analysis.",
        reh301Title: "Human Motion Analysis",
        reh301Desc: "Explores biomechanics of movement and technologies for capturing and analyzing motion.",
        reh301T1: "Kinematic Modeling",
        reh301T2: "Force Platforms",
        reh301T3: "Gait Cycle Analysis",
        reh301T4: "Wearables",
        reh302Title: "Prosthetics & Orthotics",
        reh302Desc: "Design and evaluation of assistive devices for mobility and function.",
        reh302T1: "Socket Design",
        reh302T2: "Actuation & Control",
        reh302T3: "Material Selection",
        reh302T4: "Clinical Metrics",
        reh303Title: "Rehabilitation Technologies",
        reh303Desc: "Robotics, VR-based therapy, and outcome measures.",
        reh303T1: "Exoskeletons",
        reh303T2: "FES Systems",
        reh303T3: "VR & AR",
        reh303T4: "Clinical Protocols",
        ctaExplore: "Learn by doing: explore interactive, virtual lab courses and simulations.",
        exploreCourses: "Explore Courses"
      },
      ar: {
        title: "منصة BioMed LMS – النهوض بتعليم الهندسة الطبية الحيوية",
        subtitle: "منصة نخبوية تمكّن طلاب ومهنيّي الهندسة الطبية الحيوية عبر مسارات تعلم مركّزة ومبتكرة وجاهزة للصناعة.",
        getStarted: "ابدأ الآن",
        contactUs: "تواصل معنا",
        imagingShort: "التصوير الطبي",
        imagingBlurb: "أنظمة وأجهزة MRI وCT وPET والموجات فوق الصوتية.",
        electronicsShort: "الإلكترونيات الطبية",
        electronicsBlurb: "ECG وEMG وEEG والمستشعرات واكتساب البيانات.",
        biomechShort: "الميكانيكا الحيوية",
        biomechBlurb: "التأهيل، الأطراف الصناعية، وتحليل الحركة.",
        div1Title: "تقنيات التصوير الطبي والأجهزة",
        div1Desc: "نظرة عامة على أنظمة MRI وCT والموجات فوق الصوتية وPET وتقنيات الأجهزة.",
        img101Title: "مبادئ التصوير الطبي",
        img101Desc: "مبادئ MRI وCT وPET والموجات فوق الصوتية.",
        img101T1: "فيزياء MRI",
        img101T2: "إعادة بناء CT",
        img101T3: "معالجة إشارة الموجات فوق الصوتية",
        img101T4: "عوامل تباين الصورة",
        img102Title: "الكواشف والمكونات الصلبة للتصوير",
        img102Desc: "تصميم الكواشف، التجميع، المحولات، والواجهات التناظرية.",
        img102T1: "اللمّاعات والأنابيب المضاعفة",
        img102T2: "محولات الموجات فوق الصوتية",
        img102T3: "التحويل الزمني والتماثلي/الرقمي",
        img102T4: "الضوضاء ونسبة الإشارة إلى الضوضاء",
        img103Title: "معالجة الصور وإعادة البناء",
        img103Desc: "من الترشيح والتجزئة إلى طرق إعادة البناء التكراري.",
        img103T1: "فورييه والمويجات",
        img103T2: "إعادة بناء CT/MRI التكرارية",
        img103T3: "إزالة الضوضاء",
        img103T4: "العيوب الفنية",
        div2Title: "الإلكترونيات الطبية وقياسات الأجهزة الفسيولوجية",
        div2Desc: "يشمل ECG وEMG وEEG والمستشعرات وأنظمة اكتساب البيانات.",
        elec201Title: "المستشعرات الطبية واكتساب الإشارة",
        elec201Desc: "مستشعرات مراقبة الإشارات الفسيولوجية وأساليب التحويل A/D.",
        elec201T1: "أقطاب ECG",
        elec201T2: "معايرة المستشعر",
        elec201T3: "أنظمة اكتساب البيانات",
        elec201T4: "ترشيح الإشارة",
        elec202Title: "تصميم الواجهة التناظرية",
        elec202Desc: "مضخمات منخفضة الضوضاء ومضخمات القياس ومرشحات مانعة للطي.",
        elec202T1: "بُنى INA",
        elec202T2: "CMRR والضوضاء",
        elec202T3: "الحماية والعزل",
        elec202T4: "معايير السلامة",
        elec203Title: "معالجة الإشارات الفسيولوجية",
        elec203Desc: "تحليل الزمن-التردد واستخراج الميزات لـ ECG/EMG/EEG.",
        elec203T1: "الترشيح وكثافة الطيف",
        elec203T2: "كشف قمم R",
        elec203T3: "أغلفة EMG",
        elec203T4: "نطاقات EEG",
        div3Title: "الميكانيكا الحيوية والهندسة التأهيلية",
        div3Desc: "الأطراف والأجهزة التقويمية، أجهزة التأهيل، وتحليل الحركة.",
        reh301Title: "تحليل الحركة البشرية",
        reh301Desc: "ميكانيكا الحركة وتقنيات الالتقاط والتحليل.",
        reh301T1: "النمذجة الحركية",
        reh301T2: "منصات القوة",
        reh301T3: "تحليل دورة المشي",
        reh301T4: "الأجهزة القابلة للارتداء",
        reh302Title: "الأطراف والأجهزة التقويمية",
        reh302Desc: "تصميم وتقييم الأجهزة المساعدة للحركة والوظيفة.",
        reh302T1: "تصميم التجويف",
        reh302T2: "التحريك والتحكم",
        reh302T3: "اختيار المواد",
        reh302T4: "المقاييس السريرية",
        reh303Title: "تقنيات التأهيل",
        reh303Desc: "الروبوتات، العلاج المعتمد على الواقع الافتراضي، ومقاييس النتائج.",
        reh303T1: "الهياكل الخارجية",
        reh303T2: "التحفيز الكهربائي الوظيفي",
        reh303T3: "الواقع الافتراضي والمعزز",
        reh303T4: "البروتوكولات السريرية",
        ctaExplore: "تعلّم بالممارسة: استكشف دورات ومختبرات افتراضية تفاعلية.",
        exploreCourses: "استكشاف الدورات"
      }
    };

    const $ = (sel, root=document) => root.querySelector(sel);
    const $$ = (sel, root=document) => Array.from(root.querySelectorAll(sel));
    const applyLang = (lang) => {
      const dict = i18n[lang] || i18n.en;
      $$("[data-i18n]").forEach(el => {
        const key = el.getAttribute("data-i18n");
        if (dict[key]) el.textContent = dict[key];
      });
      document.documentElement.lang = lang;
      // Toggle direction for Arabic
      if (lang === "ar") {
        document.documentElement.dir = "rtl";
      } else {
        document.documentElement.dir = "ltr";
      }
      // Toggle active button state
      $("#btn-en").classList.toggle("active", lang === "en");
      $("#btn-ar").classList.toggle("active", lang === "ar");
      $("#btn-en").setAttribute("aria-pressed", String(lang === "en"));
      $("#btn-ar").setAttribute("aria-pressed", String(lang === "ar"));
    };

    $("#btn-en")?.addEventListener("click", () => applyLang("en"));
    $("#btn-ar")?.addEventListener("click", () => applyLang("ar"));

    // Example interaction: reveal more modules dynamically (placeholder)
    // You could extend this to fetch and reveal course lists.
    // For now the page loads fast and static; language toggle is the main interaction.
    applyLang("en");
  </script>
  <!-- Module Quick Access -->
  <div style="max-width:1000px;margin:24px auto;padding:0 16px">
    <div style="background:#0f1220;border:1px solid #1f2336;border-radius:12px;padding:20px">
      <h2 style="color:#e6e9ff;margin-top:0">Start Course: Module 1 · Principles of Medical Imaging</h2>
      <p style="color:#b9bfd6">Kick off with MRI Physics, CT Reconstruction, Ultrasound Signal Processing, and Image Contrast Agents.</p>
      <div style="display:flex;gap:12px;flex-wrap:wrap">
        <a href="./modules/module-1/index.html" style="display:inline-block;padding:10px 14px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b">Open Module 1</a>
        <a href="./modules/module-1/mri.html" style="display:inline-block;padding:10px 14px;border:1px solid #2b3050;border-radius:10px;color:#e6e9ff;text-decoration:none;background:#171a2b">Go to MRI Lesson</a>
      </div>
    </div>
  </div>
</body>
</html>
