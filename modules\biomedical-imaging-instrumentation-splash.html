<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>Biomedical Imaging & Instrumentation Technology — Splash</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800;900&display=swap" rel="stylesheet">
<style>
:root{
  --bg:#101828;        /* dark base */
  --panel:#0B1220;
  --text:#E5E7EB;
  --soft:#CBD5E1;
  --accent:#2E90FA;    /* blue */
  --teal:#22D3EE;      /* teal */
  --cyan:#67E8F9;      /* CT cyan */
  --mri:#BAE6FD;       /* MRI cool white/blue */
  --us:#86EFAC;        /* Ultrasound light green */
  --pet1:#FDE047;      /* PET yellow */
  --pet2:#FB923C;      /* PET orange */
  --pet3:#EF4444;      /* PET red */
  --glow: 0 30px 80px rgba(0,0,0,.6), inset 0 1px 0 rgba(255,255,255,.03);
}
*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0; font-family:Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; color:var(--text);
  background:
    radial-gradient(1200px 900px at 70% -10%, rgba(46,144,250,0.07), transparent 60%),
    radial-gradient(900px 700px at 15% 110%, rgba(34,211,238,0.06), transparent 60%),
    var(--bg);
}
/* Container canvas-like scene */
.scene{
  height:100%;
  display:flex; flex-direction:column;
}
.header{
  padding:18px 20px 8px;
  text-align:center;
  user-select:none; -webkit-user-select:none;
}
.title{
  font-weight:900; letter-spacing:.3px; font-size:28px;
  background: linear-gradient(180deg, #E6F1FF, #98C8FF 60%, #5AAFFF);
  -webkit-background-clip:text; background-clip:text; color:transparent;
  text-shadow: 0 0 24px rgba(46,144,250,.35);
}
.subtitle{
  margin-top:6px; font-size:13px; color:var(--soft);
}
.canvas{
  position:relative; flex:1; margin:10px; border-radius:16px; overflow:hidden;
  background:
    radial-gradient(1200px 800px at 50% -20%, rgba(46,144,250,0.06), transparent 70%),
    linear-gradient(180deg, #0C1426, #0A1122 60%, #091024);
  border:1px solid rgba(255,255,255,0.06);
  box-shadow: var(--glow);
}

/* Central holographic figure */
.figure-wrap{
  position:absolute; left:50%; top:52%; transform:translate(-50%,-50%);
  width:520px; height:820px; pointer-events:none;
  filter: drop-shadow(0 20px 60px rgba(34,211,238,.25));
}
.holo-figure{
  position:absolute; inset:0; border-radius:22px;
  background:
    radial-gradient(240px 420px at 50% 20%, rgba(34,211,238,0.12), transparent 60%),
    radial-gradient(260px 520px at 50% 65%, rgba(46,144,250,0.10), transparent 66%),
    linear-gradient(180deg, rgba(34,197,255,0.18), rgba(34,211,238,0.06));
  opacity:.9; mix-blend-mode:screen;
}
.holo-wire{
  position:absolute; inset:0; border-radius:22px;
  background-image:
    linear-gradient(to right, rgba(103,232,249,.15) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(103,232,249,.15) 1px, transparent 1px);
  background-size: 22px 22px, 22px 22px; opacity:.35; mask-image: radial-gradient(60% 70% at 50% 45%, black 55%, transparent 80%);
}
/* Skeleton and organs (stylized translucent) */
.skeleton{
  position:absolute; left:50%; top:50%; transform:translate(-50%,-50%);
  width:320px; height:620px; border-radius:18px;
  background:
    radial-gradient(80px 160px at 50% 20%, rgba(190,227,248,.5), rgba(14,165,233,.1) 70%, transparent 80%),
    radial-gradient(140px 260px at 50% 60%, rgba(148,163,184,.35), rgba(15,23,42,0) 70%),
    linear-gradient(180deg, rgba(148,163,184,.25), rgba(14,116,144,.15));
  box-shadow: inset 0 0 40px rgba(0,0,0,.4);
  filter: blur(.2px) saturate(1.05);
}
/* organs */
.organ{
  position:absolute; border-radius:20px; opacity:.75; box-shadow: 0 10px 24px rgba(0,0,0,.35), inset 0 0 30px rgba(255,255,255,.06);
}
.brain{ left:50%; top:22%; transform:translate(-50%,-50%); width:120px; height:90px; background: radial-gradient(60px 40px at 50% 50%, rgba(186,230,253,.9), rgba(30,64,175,.35)); }
.heart{ left:50%; top:38%; transform:translate(-50%,-50%); width:70px; height:80px; border-radius:32px; background: radial-gradient(40px 40px at 50% 50%, rgba(244,114,182,.65), rgba(56,189,248,.25)); }
.lungs{ left:50%; top:34%; transform:translate(-50%,-50%); width:180px; height:120px; border-radius:60px; background: radial-gradient(90px 60px at 50% 50%, rgba(125,211,252,.5), rgba(2,132,199,.25)); }
.liver{ left:55%; top:48%; transform:translate(-50%,-50%); width:140px; height:70px; border-radius:40px; background: radial-gradient(70px 40px at 50% 50%, rgba(45,212,191,.55), rgba(15,118,110,.25)); }

/* MRI around head: concentric gradient arcs */
.mri-rings{
  position:absolute; left:50%; top:22%; transform:translate(-50%,-50%); width:220px; height:220px; border-radius:50%;
  pointer-events:none;
}
.ring{
  position:absolute; inset:0; border-radius:50%;
  border:2px solid rgba(186,230,253,.65);
  box-shadow: 0 0 22px rgba(186,230,253,.45), inset 0 0 22px rgba(186,230,253,.25);
  animation: sweep 4s linear infinite;
}
.ring.r2{ transform: scale(.82); animation-delay:.3s; opacity:.85 }
.ring.r3{ transform: scale(.64); animation-delay:.6s; opacity:.7 }
@keyframes sweep {
  0%{ filter: hue-rotate(0deg) brightness(1); opacity:.9 }
  50%{ filter: hue-rotate(8deg) brightness(1.08); opacity:.7 }
  100%{ filter: hue-rotate(0deg) brightness(1); opacity:.9 }
}

/* CT rotating beam around torso */
.ct-gantry{
  position:absolute; left:50%; top:40%; transform:translate(-50%,-50%); width:400px; height:400px; border-radius:50%;
  border:1px dashed rgba(103,232,249,.25); box-shadow: inset 0 0 40px rgba(103,232,249,.08);
}
.ct-beam{
  --angle: 0deg;
  position:absolute; left:50%; top:50%; width:420px; height:2px; background: linear-gradient(90deg, transparent 0%, rgba(103,232,249,.9) 40%, rgba(34,211,238,.9) 60%, transparent 100%);
  transform-origin: left center;
  transform: rotate(var(--angle));
  filter: drop-shadow(0 0 10px rgba(103,232,249,.7));
  animation: rotate 6s linear infinite;
}
@keyframes rotate { to { transform: rotate(360deg) } }

/* CT slices HUD */
.hud{
  position:absolute; top:16%; right:3%; width:280px; border-radius:12px; overflow:hidden;
  background: rgba(10,16,36,.6); border:1px solid rgba(255,255,255,.08); backdrop-filter: blur(6px); -webkit-backdrop-filter: blur(6px);
  box-shadow: var(--glow);
}
.hud-head{
  display:flex; align-items:center; justify-content:space-between; padding:10px 12px; font-size:12px; color:var(--soft);
  border-bottom:1px solid rgba(255,255,255,.06);
}
.slices{
  padding:10px; display:grid; grid-template-columns: repeat(4, 1fr); gap:6px;
}
.slice{
  position:relative; height:50px; border-radius:6px; overflow:hidden; border:1px solid rgba(103,232,249,.25);
  background:
    linear-gradient(180deg, rgba(2,132,199,.08), rgba(103,232,249,.12)),
    repeating-linear-gradient(0deg, rgba(103,232,249,.12), rgba(103,232,249,.12) 2px, transparent 2px, transparent 4px);
}
.slice::after{
  content:""; position:absolute; inset:0; background: radial-gradient(120px 50px at 60% 50%, rgba(103,232,249,.25), transparent 70%);
}

/* Ultrasound probe & waves */
.us-probe{
  position:absolute; left:35%; top:56%; width:80px; height:44px; border-radius:10px 10px 18px 18px / 10px 10px 18px 18px;
  background: linear-gradient(180deg,#F3F4F6,#CBD5E1); border:1px solid #94A3B8; box-shadow: 0 10px 24px rgba(0,0,0,.35), inset 0 -2px 0 rgba(0,0,0,.1);
  transform: rotate(-12deg);
}
.us-wave{
  position:absolute; left:40%; top:57%; width:160px; height:160px; border-radius:50%;
  border:2px solid rgba(134,239,172,.55); box-shadow: 0 0 18px rgba(134,239,172,.4), inset 0 0 18px rgba(134,239,172,.2);
  animation: ripple 2.6s ease-out infinite;
}
.us-wave.w2{ transform: scale(1.25); animation-delay:.3s; opacity:.8 }
.us-wave.w3{ transform: scale(1.5); animation-delay:.6s; opacity:.6 }
@keyframes ripple {
  0%{ transform: scale(0.9); opacity:.85 }
  100%{ transform: scale(1.8); opacity:0 }
}
/* Ultrasound HUD */
.hud-us{
  position:absolute; bottom:6%; left:4%; width:300px; border-radius:12px; overflow:hidden;
  background: rgba(10,16,36,.6); border:1px solid rgba(255,255,255,.08); backdrop-filter: blur(6px); -webkit-backdrop-filter: blur(6px);
  box-shadow: var(--glow);
}
.us-head{ padding:10px 12px; font-size:12px; color:var(--soft); border-bottom:1px solid rgba(255,255,255,.06) }
.us-body{ padding:10px; display:grid; grid-template-columns: repeat(6, 1fr); gap:2px; }
.us-px{
  width:100%; height:14px; background: #111827;
  background: linear-gradient(180deg, #2D2F31, #0B0C0E);
  filter: contrast(1.6) brightness(.9);
}
.us-px:nth-child(3n){ filter: brightness(1.15) }
.us-px:nth-child(7n){ filter: brightness(.7) }

/* PET hotspot */
.pet-hotspot{
  position:absolute; left:52%; top:38%; transform:translate(-50%,-50%);
  width:64px; height:64px; border-radius:50%;
  background: radial-gradient(circle at 50% 50%, var(--pet1), var(--pet2) 60%, var(--pet3) 100%);
  box-shadow:
    0 0 20px rgba(253,224,71,.75),
    0 0 34px rgba(251,146,60,.55),
    0 0 46px rgba(239,68,68,.45);
  animation: pulse 1.8s ease-in-out infinite;
}
@keyframes pulse {
  0%,100%{ transform:translate(-50%,-50%) scale(1) }
  50%{ transform:translate(-50%,-50%) scale(1.15) }
}
.spark{
  position:absolute; width:3px; height:3px; border-radius:50%; background: #FCA311; filter: drop-shadow(0 0 6px rgba(255,196,0,.8));
  animation: drift 1.6s ease-out infinite;
}
.spark.s2{ animation-delay:.3s }
.spark.s3{ animation-delay:.6s }
@keyframes drift{
  0%{ transform: translate(0,0); opacity:1 }
  100%{ transform: translate(20px,-28px); opacity:0 }
}

/* Labels and leader lines */
.label{
  position:absolute; font-size:12px; color:var(--soft); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px);
  background: rgba(8,13,28,.55); border:1px solid rgba(255,255,255,.08); padding:6px 8px; border-radius:8px;
  box-shadow: var(--glow);
}
.line{
  position:absolute; width:2px; background: linear-gradient(180deg, rgba(46,144,250,0), rgba(46,144,250,.7));
  box-shadow: 0 0 10px rgba(46,144,250,.35);
}

/* Background data streams */
.stream{
  position:absolute; inset:0; pointer-events:none; opacity:.18;
  background-image:
    linear-gradient(to right, rgba(46,144,250,.25) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(46,144,250,.15) 1px, transparent 1px);
  background-size: 40px 40px, 40px 40px;
  animation: pan 40s linear infinite;
}
@keyframes pan {
  from{ background-position: 0 0, 0 0 }
  to{ background-position: 400px 0, 0 400px }
}

/* Small rotating hardware models */
.hardware{
  position:absolute; right:24px; bottom:24px; display:flex; gap:10px;
}
.hw{
  width:60px; height:60px; border-radius:12px; border:1px solid rgba(255,255,255,.08);
  background: rgba(255,255,255,.03);
  display:grid; place-items:center; color:#BFE4FF; font-weight:900; font-size:12px;
  box-shadow: var(--glow);
  animation: spin 10s linear infinite;
}
@keyframes spin { to { transform: rotate(360deg) } }
</style>
</head>
<body>
<div class="scene">
  <div class="header">
    <div class="title">Biomedical Imaging & Instrumentation Technology</div>
    <div class="subtitle">Overview of MRI, CT, Ultrasound, PET imaging systems and instrumentation technologies.</div>
  </div>

  <div class="canvas" role="img" aria-label="Holographic transparent patient with MRI, CT, Ultrasound, PET visualizations and instrumentation HUD">
    <!-- Background data grid -->
    <div class="stream"></div>

    <!-- Central Holographic Figure -->
    <div class="figure-wrap">
      <div class="holo-figure"></div>
      <div class="holo-wire"></div>
      <div class="skeleton"></div>
      <div class="organ brain"></div>
      <div class="organ lungs"></div>
      <div class="organ heart"></div>
      <div class="organ liver"></div>
    </div>

    <!-- MRI around head -->
    <div class="mri-rings">
      <div class="ring r1"></div>
      <div class="ring r2"></div>
      <div class="ring r3"></div>
    </div>

    <!-- CT rotating beam and slices HUD -->
    <div class="ct-gantry">
      <div class="ct-beam"></div>
    </div>
    <div class="hud">
      <div class="hud-head">
        <div>CT Reconstruction</div>
        <div style="color:var(--cyan);font-weight:800">Slices ➜ 3D</div>
      </div>
      <div class="slices">
        <div class="slice"></div><div class="slice"></div><div class="slice"></div><div class="slice"></div>
        <div class="slice"></div><div class="slice"></div><div class="slice"></div><div class="slice"></div>
      </div>
    </div>

    <!-- Ultrasound probe and HUD -->
    <div class="us-probe"></div>
    <div class="us-wave"></div><div class="us-wave w2"></div><div class="us-wave w3"></div>
    <div class="hud-us">
      <div class="us-head">Ultrasound (Abdominal) — Sonogram</div>
      <div class="us-body">
        <!-- Faux grayscale sonogram pixels -->
        <div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div>
        <div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div>
        <div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div><div class="us-px"></div>
      </div>
    </div>

    <!-- PET hotspot + sparks -->
    <div class="pet-hotspot"></div>
    <div class="spark" style="left:52%; top:38%"></div>
    <div class="spark s2" style="left:52%; top:38%"></div>
    <div class="spark s3" style="left:52%; top:38%"></div>

    <!-- Labels and leader lines -->
    <div class="label" style="left:50%; top:10%; transform:translate(-50%,0); border-color:rgba(186,230,253,.25)">MRI: Magnetic Resonance</div>
    <div class="line" style="left:50%; top:12.5%; height:50px; transform:translateX(-1px)"></div>

    <div class="label" style="right:3%; top:8%;">CT: Computed Tomography</div>
    <div class="line" style="right:10%; top:13%; height:110px; background:linear-gradient(180deg, rgba(103,232,249,.0), rgba(103,232,249,.8))"></div>

    <div class="label" style="left:4%; bottom:22%;">Ultrasound</div>
    <div class="line" style="left:7%; bottom:22%; height:90px; background:linear-gradient(180deg, rgba(134,239,172,.0), rgba(134,239,172,.8))"></div>

    <div class="label" style="left:55%; top:30%; background:rgba(40,16,0,.45); border-color:rgba(255,196,0,.25); color:#FFE08A;">PET: Positron Emission</div>
    <div class="line" style="left:57%; top:33%; height:60px; background:linear-gradient(180deg, rgba(253,224,71,.0), rgba(239,68,68,.85))"></div>

    <!-- Small rotating hardware icons -->
    <div class="hardware">
      <div class="hw" title="Detector Crystal">CRY</div>
      <div class="hw" title="PMT">PMT</div>
      <div class="hw" title="US Array">US</div>
    </div>
  </div>
</div>
</body>
</html>
